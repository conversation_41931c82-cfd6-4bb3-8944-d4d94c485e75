export interface User {
  id: string;
  email: string;
  name: string;
  mobile: string;
  role: 'student' | 'admin';
  verified: boolean;
  profileImage?: string;
  aadharVerified: boolean;
  createdAt: Date;
}

export interface Exam {
  id: string;
  title: string;
  category: ExamCategory;
  duration: number; // in minutes
  totalMarks: number;
  passingMarks: number;
  startTime: Date;
  endTime: Date;
  instructions: string[];
  status: 'draft' | 'published' | 'active' | 'completed';
}

export type ExamCategory = 
  | 'IBPS'
  | 'LIC'
  | 'RAILWAY'
  | 'MPSC'
  | 'STATE_SERVICES'
  | 'TET'
  | 'CTET'
  | 'NEET'
  | 'JEE';

export interface Question {
  id: string;
  examId: string;
  type: 'mcq' | 'descriptive' | 'numerical';
  text: string;
  options?: string[];
  correctAnswer: string | number;
  marks: number;
}