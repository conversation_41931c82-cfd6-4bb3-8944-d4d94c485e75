import React from 'react';
import { X } from 'lucide-react';

interface ErrorPopupModalProps {
  isOpen: boolean;
  message: string;
  onClose: () => void;
  showCreateAccount?: boolean;
  onCreateAccount?: () => void;
  onOk?: () => void; // Callback for OK action
  isLoading?: boolean; // New prop for loading state
}

const ErrorPopupModal: React.FC<ErrorPopupModalProps> = ({
  isOpen,
  message,
  onClose,
  showCreateAccount = false,
  onCreateAccount,
  onOk,
  isLoading = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-500 to-red-600 px-6 py-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-white">Notification</h3>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          <p className="text-gray-700 text-center mb-4">{message}</p>
          
          {isLoading ? (
            <div className="flex justify-center">
              <div className="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12"></div>
            </div>
          ) : (
            <div className="flex justify-center gap-4">
              {onOk && (
                <button
                  onClick={() => {
                    onOk();
                    onClose();
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  OK
                </button>
              )}
              {showCreateAccount && onCreateAccount && (
                <button
                  onClick={() => {
                    onCreateAccount();
                    onClose();
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Create a New Account
                </button>
              )}
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium"
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Add CSS for the loader animation (you can put this in a CSS file or use a style tag)
const styles = `
  .loader {
    border-top-color: #3498db;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default ErrorPopupModal;