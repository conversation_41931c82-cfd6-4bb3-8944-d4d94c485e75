import React, { useEffect, useState } from "react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "../firebase-config"; // Import the Firestore instance
import { ChevronDown, ChevronRight } from "lucide-react"; // Import icons

interface Test {
    id: string;
    title: string;
    category: string;
    startTime: Date;
    duration: number;
    totalMarks: number;
    status: string;
    instructions: string;
}

const ManageTestList: React.FC = () => {
    const [tests, setTests] = useState<Test[]>([]);
    const [isCollapsed, setIsCollapsed] = useState<boolean>(false); // State for toggling visibility

    // Fetch tests data from Firestore
    useEffect(() => {
        const fetchTests = async () => {
            try {
                const querySnapshot = await getDocs(collection(db, "tests"));
                const fetchedTests: Test[] = querySnapshot.docs.map((doc) => {
                    const data = doc.data();

                    // Check if startTime is a valid Firestore Timestamp
                    const startTime = data.startTime?.seconds
                        ? new Date(data.startTime.seconds * 1000) // Convert to Date
                        : new Date(); // Default to current date if not present

                    return {
                        id: doc.id,
                        title: data.title,
                        category: data.category,
                        startTime, // Use converted or default date
                        duration: data.duration,
                        totalMarks: data.totalMarks,
                        status: data.status,
                        instructions: data.instructions,
                    };
                });
                setTests(fetchedTests); // Update state with fetched tests
            } catch (error) {
                console.error("Error fetching tests: ", error);
            }
        };

        fetchTests();
    }, []);

    const handleEdit = (id: string) => {

    };

    const handleDelete = (id: string) => {

    };

    // Toggle collapse state
    const toggleCollapse = () => {
        setIsCollapsed((prev) => !prev);
    };

    return (
        <div className="p-6 bg-white rounded shadow">
            <h2 onClick={toggleCollapse} className="text-xl font-bold mb-4 flex justify-between items-center bg-gray-100 px-4 py-2">
                Manage Tests
                <button
                    onClick={toggleCollapse}
                    className="flex items-center text-black"
                >
                    {isCollapsed ? (
                        <ChevronRight className="w-5 h-5" />
                    ) : (
                        <ChevronDown className="w-5 h-5" />
                    )}
                </button>
            </h2>

            {/* Collapsible section */}
            {!isCollapsed && (
                <ul>
                    {tests.map((test) => (
                        <li key={test.id} className="border rounded p-4 mb-2">
                            <h3 className="font-bold text-lg">{test.title}</h3>
                            <p>Category: {test.category}</p>
                            <p>Start Time: {test.startTime.toLocaleString()}</p>
                            <p>Duration: {test.duration} minutes</p>
                            <p>Total Marks: {test.totalMarks}</p>
                            <div className="flex gap-4 mt-2">
                                <button
                                    onClick={() => handleEdit(test.id)}
                                    className="bg-yellow-500 text-white px-4 py-2 rounded"
                                >
                                    Edit
                                </button>
                                <button
                                    onClick={() => handleDelete(test.id)}
                                    className="bg-red-500 text-white px-4 py-2 rounded"
                                >
                                    Delete
                                </button>
                            </div>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default ManageTestList;
