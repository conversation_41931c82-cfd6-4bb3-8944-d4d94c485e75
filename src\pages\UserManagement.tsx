"use client";

import React, { useState, useEffect, useRef } from "react";
import { collection, onSnapshot } from "firebase/firestore";
import { db, auth } from "../firebase-config";
import {
  Users,
  UserPlus,
  Search,
  Edit2,
  Trash2,
  Filter,
  Download,
  Upload,
  Calendar,
} from "lucide-react";
import { UserModal } from "../components/UserModal";
import { UserForm } from "../components/UserForm";
import { importUsersFromCSV, exportUsersToCSV } from "../utils/csvUtils";
import ErrorPopupModal from "../components/ErrorPopupModal";
import api from "../axios";
import axios from "axios";

export interface User {
  id: string;
  role: string;
  status: string;
  name: string;
  lastLogin: string | React.ReactNode;
  dateAdded: string | React.ReactNode;
  fullName: string;
  email: string;
  phone?: string;
  address: string;
  category: string;
  collegeName: string;
  gender: string;
  guardianContact: string;
  interestedCoachingProgram: string;
  lastClassAttended: string;
  professionalQualification: string;
  createdAt: string;
}

interface FirestoreUser extends User {
  uid: string;
  phone?: string;
}

interface RestStudent {
  studentId: string;
  name: string;
  email: string;
  collegeName: string;
  address: string;
  fcmToken: string;
  uid: string;
  guardianContact: string;
  phoneNumber: string;
  category: string;
  interestedCoachingProgram: string;
  professionalQualification: string;
  lastClassAttended: string;
  gender: string;
  dateOfBirth: string;
  registrationDate: string;
  role: string;
  status: string;
  lastLogin: string;
  dateAdded: string;
  createdAt: string;
  updatedAt: string;
}

function getDisplayName(user: RestStudent | FirestoreUser): string {
  return "fullName" in user ? user.fullName : user.name;
}

function getUserId(user: RestStudent | FirestoreUser): string {
  return "id" in user ? user.id : user.studentId;
}

// Normalize user data to match UserForm expectations
function normalizeUserForForm(user: RestStudent | FirestoreUser): Partial<User> {
  if ("studentId" in user) {
    // RestStudent
    return {
      fullName: user.name,
      email: user.email,
      phone: user.phoneNumber,
      address: user.address,
      category: user.category,
      collegeName: user.collegeName,
      gender: user.gender,
      guardianContact: user.guardianContact,
      interestedCoachingProgram: user.interestedCoachingProgram,
      lastClassAttended: user.lastClassAttended,
      professionalQualification: user.professionalQualification,
      role: user.role,
      status: user.status,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      dateAdded: user.dateAdded,
    };
  } else {
    // FirestoreUser
    return {
      fullName: user.fullName || user.name || "Unknown",
      email: user.email,
      phone: user.phone,
      address: user.address,
      category: user.category,
      collegeName: user.collegeName,
      gender: user.gender,
      guardianContact: user.guardianContact,
      interestedCoachingProgram: user.interestedCoachingProgram,
      lastClassAttended: user.lastClassAttended,
      professionalQualification: user.professionalQualification,
      role: user.role,
      status: user.status,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      dateAdded: user.dateAdded,
    };
  }
}

function UserManagement() {
  const [firestoreUsers, setFirestoreUsers] = useState<FirestoreUser[]>([]);
  const [restApiStudents, setRestApiStudents] = useState<RestStudent[]>([]);
  const [activeTab, setActiveTab] = useState<"firestore" | "backend">("firestore");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<RestStudent | FirestoreUser | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [dateRange, setDateRange] = useState({ start: "", end: "" });
  const [sortConfig, setSortConfig] = useState({ key: "name", direction: "asc" });
  const [isFirestoreLoading, setIsFirestoreLoading] = useState(true);
  const [isBackendLoading, setIsBackendLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const API_BASE_URL = "/api/students";

  // Fetch data with proper auth handling
  useEffect(() => {
    // Fetch Firestore users
    const usersCollectionRef = collection(db, "users");
    const unsubscribeFirestore = onSnapshot(usersCollectionRef, (snapshot) => {
      try {
        const fetchedUsers: FirestoreUser[] = snapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            uid: data.uid ?? "",
            role: data.role ?? "Applicant",
            status: data.status ?? "Access Requested",
            name: data.name ?? "", // Use actual name field if exists
            fullName: data.FullName ?? data.name ?? "", // Prefer fullName, fallback to name
            email: data.email ?? "No Email",
            phone: data.phone?.toString() ?? "",
            address: data.address ?? "",
            category: data.category ?? "general",
            collegeName: data.collegeName ?? "N/A",
            gender: data.gender ?? "N/A",
            guardianContact: data.guardianContact ?? "N/A",
            interestedCoachingProgram: data.interestedCoachingProgram ?? "N/A",
            lastClassAttended: data.lastClassAttended ?? "N/A",
            professionalQualification: data.professionalQualification ?? "N/A",
            createdAt: data.createdAt ?? new Date().toISOString(),
            lastLogin: data.lastLogin ?? new Date().toISOString(),
            dateAdded: data.dateAdded ?? new Date().toISOString(),
          };
        });
        setFirestoreUsers(fetchedUsers);
        setIsFirestoreLoading(false);
      } catch (error) {
        console.error("Error fetching Firestore users:", error);
        setErrorMessage(error instanceof Error ? error.message : "Failed to load Firestore users");
        setIsErrorPopupOpen(true);
        setIsFirestoreLoading(false);
      }
    });

    // Fetch REST API students only after auth state is confirmed
    const unsubscribeAuth = auth.onAuthStateChanged(async (user) => {
      if (user) {
        try {
          const idToken = await user.getIdToken();
          const response = await api.get(`${API_BASE_URL}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${idToken}`,
            },
          });
          const data: RestStudent[] = response.data;;
          setRestApiStudents(data);
          setIsBackendLoading(false);
        } catch (error) {
          console.error("Error fetching REST API students:", error);
          setErrorMessage(error instanceof Error ? error.message : "Failed to load REST API students");
          setIsErrorPopupOpen(true);
          setIsBackendLoading(false);
        }
      } else {
        // User not authenticated, skip REST API fetch
        setErrorMessage("User not authenticated. Please log in to view backend users.");
        setIsErrorPopupOpen(true);
        setIsBackendLoading(false);
      }
    });

    return () => {
      unsubscribeFirestore();
      unsubscribeAuth();
    };
  }, []);

  const normalizeEmail = (email?: string) => email?.trim().toLowerCase();

  // Deduplicate firestore users
  const uniqueFirestoreUsers = Array.from(
    new Map(firestoreUsers.map(u => [normalizeEmail(u.email), u])).values()
  );

  // Build approved email set
  const approvedEmails = new Set(restApiStudents.map(s => normalizeEmail(s.email)));

  // Filter unapproved users
  const unapprovedFirestoreUsers = uniqueFirestoreUsers.filter(
    user => !approvedEmails.has(normalizeEmail(user.email))
  );

  const currentUsers = activeTab === "firestore"
    ? unapprovedFirestoreUsers
    : restApiStudents;


  const filteredUsers = currentUsers
    .filter((user) => {
      const displayName = getDisplayName(user);
      const matchesSearch =
        displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (user.email || "").toLowerCase().includes(searchQuery.toLowerCase());
      const matchesRole = selectedRole === "all" || user.role === selectedRole;
      const matchesStatus = selectedStatus === "all" || user.status === selectedStatus;

      const userCreatedAt = new Date(user.createdAt).getTime();
      const startDate = dateRange.start ? new Date(dateRange.start).getTime() : null;
      const endDate = dateRange.end ? new Date(dateRange.end).getTime() : null;

      const matchesDate =
        (!startDate && !endDate) ||
        (startDate && !endDate && userCreatedAt >= startDate) ||
        (!startDate && endDate && userCreatedAt <= endDate) ||
        (startDate && endDate && userCreatedAt >= startDate && userCreatedAt <= endDate);

      return matchesSearch && matchesRole && matchesStatus && matchesDate;
    })
    .sort((a, b) => {
      const key = sortConfig.key;
      let aVal: string = "";
      let bVal: string = "";
      if (key === "name") {
        aVal = getDisplayName(a);
        bVal = getDisplayName(b);
      } else {
        aVal = (a as any)[key] || "";
        bVal = (b as any)[key] || "";
      }
      const direction = sortConfig.direction === "asc" ? 1 : -1;
      return aVal > bVal ? direction : aVal < bVal ? -direction : 0;
    });

  const handleSort = (key: string) => {
    setSortConfig((current) => ({
      key,
      direction: current.key === key && current.direction === "asc" ? "desc" : "asc",
    }));
  };

  // Add user based on active tab
  const handleAddUser = async (formData: FormData) => {
    const user = auth.currentUser;
    if (!user) {
      setErrorMessage("User not authenticated.");
      setIsErrorPopupOpen(true);
      return;
    }

    const payload: RestStudent = {
      studentId: "",
      name: (formData.get("fullName") || "").toString(),
      email: (formData.get("email") || "").toString(),
      collegeName: (formData.get("collegeName") || "").toString(),
      address: (formData.get("address") || "").toString(),
      fcmToken: "",
      uid: user.uid,
      guardianContact: (formData.get("guardianContact") || "").toString(),
      phoneNumber: (formData.get("phone") || "").toString(),
      category: (formData.get("category") || "").toString(),
      interestedCoachingProgram: (formData.get("interestedCoachingProgram") || "").toString(),
      professionalQualification: (formData.get("professionalQualification") || "").toString(),
      lastClassAttended: (formData.get("lastClassAttended") || "").toString(),
      gender: (formData.get("gender") || "").toString(),
      dateOfBirth: new Date().toISOString(),
      registrationDate: new Date().toISOString(),
      role: (formData.get("role") || "STUDENT").toString().toUpperCase(),
      status: (formData.get("status") || "ACTIVE").toString().toUpperCase(),
      lastLogin: new Date().toISOString(),
      dateAdded: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const idToken = await user.getIdToken();
      const response = await api.post(API_BASE_URL, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${idToken}`,
        },
      });

      const newStudent: RestStudent = response.data; // Axios automatically parses the response data
      setRestApiStudents((prev) => [...prev, newStudent]);
    } catch (error) {
      console.error('Error adding backend user:', error);
      setErrorMessage(
        axios.isAxiosError(error)
          ? error.message
          : 'Failed to add backend user'
      );
      setIsErrorPopupOpen(true);
    }

    setIsAddModalOpen(false);
  };

  // Edit user based on active tab
  const handleEditUser = async (formData: FormData) => {
    if (!selectedUser) return;
    const user = auth.currentUser;
    if (!user) {
      setErrorMessage("User not authenticated.");
      setIsErrorPopupOpen(true);
      return;
    }

    //if (activeTab === "backend") {
    const payload: RestStudent = {
      studentId: getUserId(selectedUser),
      name: (formData.get("fullName") || selectedUser.name).toString(),
      email: (formData.get("email") || selectedUser.email).toString(),
      collegeName: (formData.get("collegeName") || selectedUser.collegeName).toString(),
      address: (formData.get("address") || selectedUser.address).toString(),
      fcmToken: (selectedUser as RestStudent).fcmToken || "",
      uid: (selectedUser as RestStudent).uid || user.uid,
      guardianContact: (formData.get("guardianContact") || selectedUser.guardianContact).toString(),
      phoneNumber: (formData.get("phone") || (selectedUser as RestStudent).phoneNumber || (selectedUser as FirestoreUser).phone || "").toString(),
      category: (formData.get("category") || selectedUser.category).toString(),
      interestedCoachingProgram: (formData.get("interestedCoachingProgram") || selectedUser.interestedCoachingProgram).toString(),
      professionalQualification: (formData.get("professionalQualification") || selectedUser.professionalQualification).toString(),
      lastClassAttended: (formData.get("lastClassAttended") || selectedUser.lastClassAttended).toString(),
      gender: (formData.get("gender") || selectedUser.gender).toString(),
      dateOfBirth: (selectedUser as RestStudent).dateOfBirth || new Date().toISOString(),
      registrationDate: (selectedUser as RestStudent).registrationDate || new Date().toISOString(),
      role: (formData.get("role") || selectedUser.role).toString().toUpperCase(),
      status: (formData.get("status") || selectedUser.status).toString().toUpperCase(),
      lastLogin: (selectedUser as RestStudent).lastLogin || new Date().toISOString(),
      dateAdded: (selectedUser as RestStudent).dateAdded || new Date().toISOString(),
      createdAt: selectedUser.createdAt,
      updatedAt: new Date().toISOString(),
    };

    try {
      const idToken = await user.getIdToken();
      const response = await api.post(API_BASE_URL, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${idToken}`,
        },
      });

      const updatedStudent: RestStudent = response.data; // Axios automatically parses the response data
      setRestApiStudents((prev) =>
        prev.map((student) =>
          student.studentId === updatedStudent.studentId ? updatedStudent : student
        )
      );
    } catch (error) {
      console.error('Error editing backend user:', error);
      setErrorMessage(
        'Failed to edit backend user'
      );
      setIsErrorPopupOpen(true);
    }
    // } else {
    //   setErrorMessage("Firestore user editing not implemented yet.");
    // setIsErrorPopupOpen(true);
    //}
    setIsEditModalOpen(false);
  };

  // Delete users based on active tab
  const handleDeleteUsers = async () => {
    const user = auth.currentUser;
    if (!user) {
      setErrorMessage("User not authenticated.");
      setIsErrorPopupOpen(true);
      return;
    }

    if (activeTab === "backend") {
      try {
        const idToken = await user.getIdToken();
        await Promise.all(
          selectedUsers.map((id) =>
            fetch(`${API_BASE_URL}/${id}`, {
              method: "DELETE",
              headers: {
                Authorization: `Bearer ${idToken}`,
              },
            }).then((res) => {
              if (!res.ok) throw new Error(`Failed to delete user ${id}`);
            })
          )
        );
        setRestApiStudents((prev) => prev.filter((student) => !selectedUsers.includes(student.studentId)));
      } catch (error) {
        console.error("Error deleting backend users:", error);
        setErrorMessage(error instanceof Error ? error.message : "Failed to delete backend users");
        setIsErrorPopupOpen(true);
      }
    } else {
      setErrorMessage("Firestore user deletion not implemented yet.");
      setIsErrorPopupOpen(true);
    }
    setSelectedUsers([]);
    setIsDeleteModalOpen(false);
  };

  const handleImportUsers = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const importedUsers = await importUsersFromCSV(file);
        if (activeTab === "backend") {
          const user = auth.currentUser;
          if (!user) throw new Error("User not authenticated.");
          const idToken = await user.getIdToken();

          const importedStudents = await Promise.all(
            importedUsers.map((u) =>
              fetch(`${API_BASE_URL}`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${idToken}`,
                },
                body: JSON.stringify({ ...u, uid: user.uid }),
              }).then((res) => res.data)
            )
          );
          setRestApiStudents((prev) => [...prev, ...importedStudents]);
        } else {
          setErrorMessage("Firestore user import not implemented yet.");
          setIsErrorPopupOpen(true);
        }
        alert(`Successfully imported ${importedUsers.length} users.`);
      } catch (error) {
        console.error("Error importing users:", error);
        setErrorMessage(error instanceof Error ? error.message : "Failed to import users");
        setIsErrorPopupOpen(true);
      }
    }
  };

  const handleExportUsers = () => {
    try {
      exportUsersToCSV(currentUsers as any);
      alert("Users exported successfully.");
    } catch (error) {
      console.error("Error exporting users:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to export users");
      setIsErrorPopupOpen(true);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "admin":
        return "bg-purple-100 text-purple-800";
      case "instructor":
        return "bg-blue-100 text-blue-800";
      case "student":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-red-100 text-red-800";
      case "access requested":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isFirestoreLoading || isBackendLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-gray-600 text-lg font-semibold animate-pulse">Loading users...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Users className="h-6 w-6 text-gray-600" />
          <h1 className="text-2xl font-semibold text-gray-900">User Management</h1>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            <UserPlus className="h-5 w-5 mr-2" />
            Add New User
          </button>
          <button
            onClick={handleExportUsers}
            className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Download className="h-5 w-5 mr-2" />
            Export CSV
          </button>
          <label className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
            <Upload className="h-5 w-5 mr-2" />
            Import CSV
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept=".csv"
              onChange={handleImportUsers}
            />
          </label>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("backend")}
            className={`${activeTab === "backend"
              ? "border-primary-500 text-primary-600"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Approved Students ({restApiStudents.length})
          </button>
          <button
            onClick={() => setActiveTab("firestore")}
            className={`${activeTab === "firestore"
              ? "border-primary-500 text-primary-600"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Awaiting Approval Users ({unapprovedFirestoreUsers.length})
          </button>
        </nav>
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter className="h-5 w-5 text-gray-400" />
              </div>
              <select
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                <option value="all">All Roles</option>
                <option value="ADMIN">Admin</option>
                <option value="INSTRUCTOR">Instructor</option>
                <option value="STUDENT">Student</option>
              </select>
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter className="h-5 w-5 text-gray-400" />
              </div>
              <select
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
                <option value="SUSPENDED">Suspended</option>
                <option value="ACCESS_REQUESTED">Access Requested</option>
              </select>
            </div>

            <div className="relative">
              <div className="flex space-x-2">
                <div className="relative w-1/2">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="date"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    value={dateRange.start}
                    onChange={(e) => setDateRange((prev) => ({ ...prev, start: e.target.value }))}
                  />
                </div>
                <div className="relative w-1/2">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="date"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    value={dateRange.end}
                    onChange={(e) => setDateRange((prev) => ({ ...prev, end: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {selectedUsers.length > 0 && (
          <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">{selectedUsers.length} users selected</span>
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsDeleteModalOpen(true)}
                  className="px-3 py-1 text-sm text-red-600 border border-red-600 rounded-md hover:bg-red-50"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    onChange={(e) =>
                      setSelectedUsers(e.target.checked ? filteredUsers.map((u) => getUserId(u)) : [])
                    }
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("name")}
                >
                  User
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("role")}
                >
                  Role
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("status")}
                >
                  Status
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("lastLogin")}
                >
                  Last Login
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("dateAdded")}
                >
                  Date Added
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={getUserId(user)}>
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(getUserId(user))}
                      onChange={(e) =>
                        setSelectedUsers((current) =>
                          e.target.checked
                            ? [...current, getUserId(user)]
                            : current.filter((id) => id !== getUserId(user))
                        )
                      }
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <img
                          className="h-10 w-10 rounded-full"
                          src={`https://ui-avatars.com/api/?name=${encodeURIComponent(getDisplayName(user))}&background=random`}
                          alt={getDisplayName(user)}
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{getDisplayName(user)}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(
                        user.role
                      )}`}
                    >
                      {user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        user.status
                      )}`}
                    >
                      {user.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.lastLogin as string).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.dateAdded as string).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-3">
                      <button
                        className="text-primary-600 hover:text-primary-900"
                        onClick={() => {
                          setSelectedUser(user);
                          setIsEditModalOpen(true);
                        }}
                      >
                        <Edit2 className="h-5 w-5" />
                      </button>
                      <button
                        className="text-red-600 hover:text-red-900"
                        onClick={() => {
                          setSelectedUsers([getUserId(user)]);
                          setIsDeleteModalOpen(true);
                        }}
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <UserModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title="Add New User">
        <UserForm onSubmit={handleAddUser} />
      </UserModal>

      <UserModal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="Edit User">
        <UserForm
          onSubmit={handleEditUser}
          initialData={selectedUser ? normalizeUserForForm(selectedUser) : undefined}
          isEdit
        />
      </UserModal>

      <UserModal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} title="Delete User">
        <div className="space-y-4">
          <p className="text-gray-500">
            Are you sure you want to delete {selectedUsers.length > 1 ? `${selectedUsers.length} users` : "this user"}?
            This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setIsDeleteModalOpen(false)}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteUsers}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>
      </UserModal>

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || "An error occurred"}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => setIsErrorPopupOpen(false)}
      />
    </div>
  );
}

export default UserManagement;