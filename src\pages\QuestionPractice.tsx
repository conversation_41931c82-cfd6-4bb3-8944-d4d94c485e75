"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { auth } from "../firebase-config"
import axios from "axios"
import ReportQuestionModal from "../components/ReportQuestionModal"
import ErrorPopupModal from "../components/ErrorPopupModal"
import api from "../axios"

interface Question {
    questionId: string
    question: string
    options: string[]
    correctAnswer: string
    explanation: string
    mark: number
    negativeMark: number
    duration: number
    difficultyLevel: string
    questionType: string
    categoryId: string
    topicId: string
    images: any[]
    tags: string[]
    createdAt: string
    updatedAt: string
}

interface Category {
    id: string
    name: string
    description: string
    topics: Topic[]
}

interface Topic {
    id: string
    name: string
    categoryId: string
    description: string
}

interface Filters {
    categoryId: string
    topicId: string
    difficultyLevel: string
}

const QuestionPractice: React.FC = () => {
    const [allQuestions, setAllQuestions] = useState<Question[]>([])
    const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([])
    const [questions, setQuestions] = useState<Question[]>([])
    const [categories, setCategories] = useState<Category[]>([])
    const [filters, setFilters] = useState<Filters>({
        categoryId: "",
        topicId: "",
        difficultyLevel: "",
    })
    const [currentPage, setCurrentPage] = useState<number>(0)
    const [totalPages, setTotalPages] = useState<number>(0)
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0)
    const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
    const [isCorrect, setIsCorrect] = useState<boolean | null>(null)
    const [answerExplanation, setAnswerExplanation] = useState<string>("")
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [isReportModalOpen, setIsReportModalOpen] = useState<boolean>(false)
    const [isErrorPopupOpen, setIsErrorPopupOpen] = useState<boolean>(false)
    const [errorMessage, setErrorMessage] = useState<string | null>(null)
    const [isRandomMode, setIsRandomMode] = useState<boolean>(false) // Default is Normal mode
    const [isFilterWarningOpen, setIsFilterWarningOpen] = useState<boolean>(false)
    const QUESTIONS_PER_PAGE = 100

    const getCategoryName = (categoryId: string) => {
        const category = categories.find((cat) => cat.id === categoryId)
        return category ? formatName(category.name) : "Unknown Category"
    }

    const getTopicName = (categoryId: string, topicId: string) => {
        const category = categories.find((cat) => cat.id === categoryId)
        const topic = category?.topics.find((top) => top.id === topicId)
        return topic ? formatName(topic.name) : "Unknown Topic"
    }

    // Fetch categories
    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setIsLoading(true)
                    const idToken = await firebaseUser.getIdToken()
                    if (!idToken) throw new Error("Failed to get user ID token")
                    const response = await api.get("/api/categories/with-topics", {
                        headers: { Authorization: `Bearer ${idToken}` },
                    })
                    const data = response.data;
                    setCategories(data)
                } catch (error) {
                    console.error("Error fetching categories:", error)
                    setErrorMessage(error instanceof Error ? error.message : "Failed to load categories")
                    setIsErrorPopupOpen(true)
                } finally {
                    setIsLoading(false)
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.")
                setIsErrorPopupOpen(true)
                setIsLoading(false)
            }
        })
        return () => unsubscribe()
    }, [])

    // Fetch questions
    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setIsLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) throw new Error("Failed to get user ID token");

                    let url = "/api/questions";
                    const params = new URLSearchParams();

                    if (isRandomMode) {
                        params.append("page", "0"); // always fetch page 0 in random mode.
                        params.append("size", "1000"); // fetch 1000 questions in one go
                    } else {
                        params.append("page", String(currentPage));
                        params.append("size", String(QUESTIONS_PER_PAGE));
                        if (filters.categoryId) params.append("categoryId", filters.categoryId);
                        if (filters.topicId) params.append("topicId", filters.topicId);
                        if (filters.difficultyLevel) params.append("difficultyLevel", filters.difficultyLevel);
                    }

                    const response = await api.get(url, {
                        headers: { Authorization: `Bearer ${idToken}` },
                        params, // Pass the query parameters here
                    });
                    const data = response.data;

                    if (isRandomMode) {
                        // Shuffle the questions before setting them
                        const shuffledQuestions = shuffleArray(data.content);
                        setAllQuestions(shuffledQuestions);
                    } else {
                        setQuestions(data.content);
                        setTotalPages(data.totalPages);
                        setCurrentQuestionIndex(0);
                        setSelectedAnswer(null);
                        setIsCorrect(null);
                        setAnswerExplanation("");
                    }
                } catch (error) {
                    console.error("Error fetching questions:", error);
                    setErrorMessage(error instanceof Error ? error.message : "Failed to load questions");
                    setIsErrorPopupOpen(true);
                } finally {
                    setIsLoading(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.")
                setIsErrorPopupOpen(true)
                setIsLoading(false)
            }
        })
        return () => unsubscribe()
    }, [isRandomMode ? null : filters, isRandomMode ? null : currentPage, isRandomMode])

    // Client-side filtering and pagination for random mode
    useEffect(() => {
        if (isRandomMode) {
            // let filtered = [...allQuestions] // now no need to shuffle again here because all question is already shuffled
            // filtered = [...filtered].sort(() => Math.random() - 0.5)

            const total = Math.ceil(allQuestions.length / QUESTIONS_PER_PAGE)
            setTotalPages(total)
            const start = currentPage * QUESTIONS_PER_PAGE
            const end = Math.min(start + QUESTIONS_PER_PAGE, allQuestions.length)
            setFilteredQuestions(allQuestions.slice(start, end))
            setCurrentQuestionIndex(0)
        }
    }, [allQuestions, currentPage, isRandomMode])

    const handleAnswerSelection = (answer: string) => {
        const currentQuestion = isRandomMode ? filteredQuestions[currentQuestionIndex] : questions[currentQuestionIndex]
        setSelectedAnswer(answer)
        setIsCorrect(answer === currentQuestion.correctAnswer)
        setAnswerExplanation(currentQuestion.explanation)
    }

    const handleNext = () => {
        setSelectedAnswer(null)
        setIsCorrect(null)
        setAnswerExplanation("")
        const currentList = isRandomMode ? filteredQuestions : questions
        if (currentQuestionIndex < currentList.length - 1) {
            setCurrentQuestionIndex((prev) => prev + 1)
        } else if (currentPage < totalPages - 1) {
            setCurrentPage((prev) => prev + 1)
            setCurrentQuestionIndex(0)
        }
    }

    const handlePrevious = () => {
        setSelectedAnswer(null)
        setIsCorrect(null)
        setAnswerExplanation("")
        const currentList = isRandomMode ? filteredQuestions : questions
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex((prev) => prev - 1)
        } else if (currentPage > 0) {
            setCurrentPage((prev) => prev - 1)
            setCurrentQuestionIndex(QUESTIONS_PER_PAGE - 1)
        }
    }

    const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>, filterType: keyof Filters) => {
        if (isRandomMode) {
            setIsFilterWarningOpen(true)
            return
        }
        const value = e.target.value
        setFilters((prevFilters) => ({
            ...prevFilters,
            [filterType]: value,
        }))
        setCurrentPage(0)
        setCurrentQuestionIndex(0)
    }

    const submitReport = async (reportType: string, message: string) => {
        return new Promise<void>((resolve, reject) => {
            const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
                unsubscribe()
                if (firebaseUser) {
                    try {
                        const idToken = await firebaseUser.getIdToken()
                        if (!idToken) throw new Error("Failed to get user ID token")
                        const reportData = {
                            questionId: (isRandomMode ? filteredQuestions : questions)[currentQuestionIndex].questionId,
                            reportType,
                            message,
                        }
                        await api.post("/api/reported-questions", reportData, {
                            headers: {
                                Authorization: `Bearer ${idToken}`,
                                "Content-Type": "application/json",
                            },
                        })
                        resolve()
                    } catch (err) {
                        console.error("Error submitting report:", err)
                        setErrorMessage(err instanceof Error ? err.message : "Failed to submit report")
                        setIsErrorPopupOpen(true)
                        reject(err)
                    }
                } else {
                    const err = new Error("User not logged in, please change tab and try again.")
                    setErrorMessage(err.message)
                    setIsErrorPopupOpen(true)
                    reject(err)
                }
            })
        })
    }

    const toggleMode = () => {
        setIsRandomMode((prev) => !prev)
        setCurrentPage(0)
        setCurrentQuestionIndex(0)
        setSelectedAnswer(null)
        setIsCorrect(null)
        setAnswerExplanation("")
        if (!isRandomMode) {
            // Switching to Random mode
            setFilters({ categoryId: "", topicId: "", difficultyLevel: "" })
        }
    }

    if (isLoading) {
        return <div className="container mx-auto px-4 py-6 text-center">Loading questions...</div>
    }

    const formatName = (name?: string) => {
        if (!name) return ""
        return name.replace(/-/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())
    }

    const getDifficultyEmoji = (difficulty: string) => {
        switch (difficulty.toUpperCase()) {
            case "EASY":
                return "🙂★☆☆"
            case "MEDIUM":
                return "😐★★☆"
            case "HARD":
                return "😣★★★"
            default:
                return "⚪"
        }
    }
    const shuffleArray = (array: Question[]): Question[] => {
        const shuffled = [...array]; // Create a copy to avoid modifying the original array
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    };
    const currentQuestion = isRandomMode ? filteredQuestions[currentQuestionIndex] : questions[currentQuestionIndex]
    const currentList = isRandomMode ? filteredQuestions : questions

    return (
        <div className="container mx-auto px-4 py-6">
            {/* Mode Toggle - Centered at the Top */}
            <div className="flex justify-center mb-6">
                <div className="flex items-center space-x-3">
                    <span className={`text-sm sm:text-base font-medium ${!isRandomMode ? "text-blue-600" : "text-gray-500"}`}>
                        Normal Mode
                    </span>
                    <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" checked={isRandomMode} onChange={toggleMode} className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                    <span className={`text-sm sm:text-base font-medium ${isRandomMode ? "text-blue-600" : "text-gray-500"}`}>
                        Random Mode
                    </span>
                </div>
            </div>

            {/* Filters Section */}
            <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 mb-6">
                <div className="w-full sm:w-1/3">
                    <label className="block text-gray-700 text-sm sm:text-base">Category:</label>
                    <select
                        className="mt-1 p-2 border rounded w-full text-sm sm:text-base disabled:bg-gray-200 disabled:cursor-not-allowed"
                        onChange={(e) => handleFilterChange(e, "categoryId")}
                        value={filters.categoryId}
                        disabled={isRandomMode}
                    >
                        <option value="">All Categories</option>
                        {categories.map((category) => (
                            <option key={category.id} value={category.id}>
                                {formatName(category.name)}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="w-full sm:w-1/3">
                    <label className="block text-gray-700 text-sm sm:text-base">Topic:</label>
                    <select
                        className="mt-1 p-2 border rounded w-full text-sm sm:text-base disabled:bg-gray-200 disabled:cursor-not-allowed"
                        onChange={(e) => handleFilterChange(e, "topicId")}
                        value={filters.topicId}
                        disabled={isRandomMode || (!filters.categoryId && categories.length > 0)}
                    >
                        <option value="">All Topics</option>
                        {categories
                            .find((category) => category.id === filters.categoryId)
                            ?.topics.map((topic) => (
                                <option key={topic.id} value={topic.id}>
                                    {formatName(topic.name)}
                                </option>
                            ))}
                    </select>
                </div>

                <div className="w-full sm:w-1/3">
                    <label className="block text-gray-700 text-sm sm:text-base">Difficulty:</label>
                    <select
                        className="mt-1 p-2 border rounded w-full text-sm sm:text-base disabled:bg-gray-200 disabled:cursor-not-allowed"
                        onChange={(e) => handleFilterChange(e, "difficultyLevel")}
                        value={filters.difficultyLevel}
                        disabled={isRandomMode}
                    >
                        <option value="">All Difficulties</option>
                        <option value="EASY">Easy</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="HARD">Hard</option>
                    </select>
                </div>
            </div>

            {!isLoading && currentList.length === 0 && (
                <div className="text-red-500 font-bold text-center text-sm sm:text-base">
                    No questions found.
                    {!isRandomMode && filters.categoryId && (
                        <> in category "{formatName(categories.find((c) => c.id === filters.categoryId)?.name || "")}"</>
                    )}
                    {!isRandomMode && filters.topicId && (
                        <>
                            {" "}
                            under topic "
                            {formatName(
                                categories.find((c) => c.id === filters.categoryId)?.topics.find((t) => t.id === filters.topicId)
                                    ?.name || "",
                            )}
                            "
                        </>
                    )}
                    {!isRandomMode && filters.difficultyLevel && <> with difficulty "{filters.difficultyLevel}"</>}
                </div>
            )}

            {currentList.length > 0 && (
                <div className="bg-white p-4 sm:p-6 rounded shadow-md">
                    <div className="mb-4 text-gray-600 text-sm sm:text-base">
                        <p>Category: {getCategoryName(currentQuestion.categoryId)}</p>
                        <p>Topic: {getTopicName(currentQuestion.categoryId, currentQuestion.topicId)}</p>
                        <p>
                            Difficulty:{" "}
                            <span>
                                {getDifficultyEmoji(currentQuestion.difficultyLevel)}{" "}
                                {currentQuestion.difficultyLevel.charAt(0).toUpperCase() +
                                    currentQuestion.difficultyLevel.slice(1).toLowerCase()}
                            </span>
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                        <h2 className="text-lg sm:text-2xl font-semibold">
                            {`Question ${currentQuestionIndex + 1 + currentPage * QUESTIONS_PER_PAGE}: ${currentQuestion.question}`}
                        </h2>
                        <button
                            onClick={() => setIsReportModalOpen(true)}
                            className="mt-2 sm:mt-0 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm sm:text-base"
                        >
                            Report Question
                        </button>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                        {currentQuestion.options.map((option, index) => {
                            const isSelected = selectedAnswer === option
                            const isCorrectAnswer = option === currentQuestion.correctAnswer
                            let optionClasses =
                                "py-2 px-4 border rounded text-white flex items-center justify-between text-sm sm:text-base"

                            if (selectedAnswer) {
                                if (isSelected) {
                                    optionClasses += isCorrectAnswer ? " bg-green-500" : " bg-red-500"
                                } else if (isCorrectAnswer) {
                                    optionClasses += " bg-green-700"
                                } else {
                                    optionClasses += " bg-gray-400"
                                }
                            } else {
                                optionClasses += " bg-gray-400"
                            }

                            return (
                                <button
                                    key={index}
                                    className={optionClasses}
                                    onClick={() => handleAnswerSelection(option)}
                                    disabled={isLoading || selectedAnswer !== null}
                                >
                                    <span>{option}</span>
                                    {isSelected && (
                                        <span className="ml-2">{isCorrectAnswer ? "Correct Answer👍 ✅" : "Your answer ✘ 👎"}</span>
                                    )}
                                    {selectedAnswer && !isSelected && isCorrectAnswer && <span className="ml-2">✅</span>}
                                </button>
                            )
                        })}
                    </div>

                    {selectedAnswer && (
                        <div className="mt-4 p-4 bg-gray-100 border rounded text-sm sm:text-base">
                            <p className="font-bold flex items-center">{isCorrect ? "Correct! 👍 ✅" : "Wrong! ❌ 👎"}</p>
                            <p className="mt-2">{answerExplanation}</p>
                            <div className="mt-2 text-gray-600 text-xs sm:text-sm">
                                <p>Duration: {currentQuestion.duration}s</p>
                            </div>
                        </div>
                    )}

                    <div className="mt-6 flex flex-col sm:flex-row justify-between space-y-2 sm:space-y-0 sm:space-x-4">
                        <button
                            onClick={handlePrevious}
                            className="py-2 px-4 bg-gray-500 text-white rounded disabled:bg-gray-300 w-full sm:w-auto text-sm sm:text-base"
                            disabled={currentPage === 0 && currentQuestionIndex === 0}
                        >
                            Previous
                        </button>
                        <button
                            onClick={handleNext}
                            className="py-2 px-4 bg-blue-500 text-white rounded disabled:bg-gray-300 w-full sm:w-auto text-sm sm:text-base"
                            disabled={currentPage === totalPages - 1 && currentQuestionIndex === currentList.length - 1}
                        >
                            Next
                        </button>
                    </div>
                </div>
            )}

            {currentList.length > 0 && (
                <ReportQuestionModal
                    isOpen={isReportModalOpen}
                    onClose={() => setIsReportModalOpen(false)}
                    questionId={currentQuestion.questionId}
                    onReport={submitReport}
                />
            )}

            <ErrorPopupModal
                isOpen={isErrorPopupOpen}
                message={errorMessage || "An error occurred"}
                onClose={() => setIsErrorPopupOpen(false)}
                onOk={() => {
                    setIsErrorPopupOpen(false)
                    window.location.reload()
                }}
            />

            <ErrorPopupModal
                isOpen={isFilterWarningOpen}
                message="Filters are disabled in Random Mode. Please switch to Normal Mode to use filters."
                onClose={() => setIsFilterWarningOpen(false)}
                onOk={() => setIsFilterWarningOpen(false)}
            />
        </div>
    )
}

export default QuestionPractice

