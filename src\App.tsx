// src/App.tsx
import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Register from './pages/Register';
import ExamList from './pages/ExamList';
import ExamDetails from './pages/ExamDetails';
import Profile from './pages/Profile';
import ProtectedRoute from './components/ProtectedRoute';
import ExamPage from './pages/ExamPage';
import ExamOverviewPage from './pages/ExamOverviewPage';
import ExamResultScreen from './pages/ExamResultScreen';
import VerifyEmail from './pages/VerifyEmail';
import UserManagement from './pages/UserManagement';
import TestManagementPage from './pages/TestManagementPage';
import QuestionBankManagement from './pages/QuestionBankManagement';
import QuestionPractice from './pages/QuestionPractice';
import Unauthorized from './pages/Unauthorized';
import { ROLES } from './constants/roles';
import Landing from './pages/Landing';
import ViewExamQuestions from './pages/ViewExamQuestions';
import AddQuestionsToExam from './pages/AddQuestionsToExam';
import ForgotPassword from './pages/ForgotPassword';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <Routes>
          {/* Public Routes */}

          <Route path="/" element={<Landing />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/verify-email" element={<VerifyEmail />} />
          <Route path="/exams-start/:examId" element={<ExamOverviewPage />} /> {/* Add this route */}
          <Route element={<Layout />}>
            <Route path="/unauthorized" element={<Unauthorized />} />
          </Route>

          {/* Protected Routes */}
          <Route element={<ProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.STUDENT, ROLES.INSTRUCTOR, ROLES.USER, ROLES.GUEST]} />}>
            <Route element={<Layout />}>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/exams" element={<ExamList />} />
              <Route path="/practice" element={<QuestionPractice />} />
              <Route path="/results" element={<ExamResultScreen />} />
              <Route path="/exams/:examId" element={<ExamDetails />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/result" element={<ExamResultScreen />} />
            </Route>
          </Route>

          {/* Admin-Only Routes */}
          <Route element={<ProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.INSTRUCTOR]} />}>
            <Route element={<Layout />}>
              <Route path="/exams-management" element={<TestManagementPage />} />
              <Route path="/exams_management/view-questions/:examId" element={<ViewExamQuestions />} />
              <Route path="/exams_management/add-questions/:examId" element={<AddQuestionsToExam />} />
              <Route path="/user-management" element={<UserManagement />} />
              <Route path="/questions-management" element={<QuestionBankManagement />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;