// src/pages/ExamOverviewPage.tsx
import React, { useState, useEffect } from "react";
import AppBar from "../components/AppBar";
import { useNavigate, useParams } from "react-router-dom";
import QuestionCard from "../components/QuestionCard";
import AnswerOption from "../components/AnswerOption";
import QuestionNavigatorModal from "../components/QuestionNavigatorModal";
import ReportQuestionModal from "../components/ReportQuestionModal";
import { Loader2 } from "lucide-react";
import { auth } from "../firebase-config";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import { AlertTriangle } from "lucide-react"; // Icon for the report button
import api from "../axios";

const API_BASE_URL = "/api";

interface QuestionType {
    questionId: string;
    question: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
    mark: number;
    negativeMark: number;
    duration: number;
    difficultyLevel: string;
    questionType: string;
    categoryId: string;
    topicId: string;
    images: string[];
    tags: string[];
    createdAt: string;
    updatedAt: string;
}

const ExamOverviewPage: React.FC = () => {
    const { examId } = useParams<{ examId: string }>();
    const duration = 600;
    const navigate = useNavigate();

    const [questions, setQuestions] = useState<QuestionType[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [timeLeft, setTimeLeft] = useState(duration);
    const [selectedAnswers, setSelectedAnswers] = useState<Record<number, string>>({});
    const [reviewLater, setReviewLater] = useState<Set<number>>(new Set());
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [attemptId, setAttemptId] = useState<string | null>(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [startTime] = useState<Date>(new Date());
    const [violations, setViolations] = useState<{ message: string; timestamp: Date }[]>([]);
    const [showChatbox, setShowChatbox] = useState(false);
    const [isChatExpanded, setIsChatExpanded] = useState(false);
    const [showWarning, setShowWarning] = useState(false);
    const [violationMessage, setViolationMessage] = useState("");
    // New state for report modal
    const [isReportModalOpen, setIsReportModalOpen] = useState(false);

    const currentQuestion = questions[currentQuestionIndex];

    const counterBadgeStyle: React.CSSProperties = {
        position: "fixed",
        top: "80px",
        right: "20px",
        zIndex: 1000,
        background: "#ef4444",
        color: "white",
        borderRadius: "50%",
        width: "32px",
        height: "32px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
        boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
    };

    const chatboxStyle: React.CSSProperties = {
        position: "fixed",
        bottom: "80px",
        right: "20px",
        zIndex: 1000,
        width: "300px",
        background: "white",
        borderRadius: "8px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        transition: "all 0.3s ease",
        maxHeight: isChatExpanded ? "400px" : "40px",
        overflow: "hidden",
    };

    const addViolation = (message: string) => {
        setViolations((prev) => [...prev, { message, timestamp: new Date() }]);
    };

    useEffect(() => {
        const handleContextMenu = (e: MouseEvent) => {
            e.preventDefault();
            addViolation("Right-click is disabled during the exam.");
            setViolationMessage(
                "Please note that right-clicking is disabled during the exam. We kindly request your cooperation in maintaining exam integrity. Any suspicious activity, including attempts to bypass restrictions, will be recorded by our system and reviewed by the examination board. Repeated violations may lead to disqualification. Thank you for adhering to the rules."
            );
            setShowWarning(true);
        };
        document.addEventListener("contextmenu", handleContextMenu);
        return () => document.removeEventListener("contextmenu", handleContextMenu);
    }, []);

    useEffect(() => {
        const handleCopyPaste = (e: ClipboardEvent) => {
            e.preventDefault();
            addViolation("Copy-paste is disabled during the exam.");
            setViolationMessage(
                "Copy-paste is disabled during the exam. We kindly request your cooperation in maintaining exam integrity. Any suspicious activity, including attempts to bypass restrictions, will be recorded by our system and reviewed by the examination board. Repeated violations may lead to disqualification. Thank you for adhering to the rules."
            );
            setShowWarning(true);
        };
        document.addEventListener("copy", handleCopyPaste);
        document.addEventListener("paste", handleCopyPaste);
        return () => {
            document.removeEventListener("copy", handleCopyPaste);
            document.removeEventListener("paste", handleCopyPaste);
        };
    }, []);

    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.visibilityState === "hidden") {
                addViolation("Switching tabs is not allowed during the exam.");
                setViolationMessage(
                    "Switching tabs is not allowed during the exam. We kindly request your cooperation in maintaining exam integrity. Any suspicious activity, including attempts to bypass restrictions, will be recorded by our system and reviewed by the examination board. Repeated violations may lead to disqualification. Thank you for adhering to the rules."
                );
                setShowWarning(true);
            }
        };
        document.addEventListener("visibilitychange", handleVisibilityChange);
        return () => document.removeEventListener("visibilitychange", handleVisibilityChange);
    }, []);

    useEffect(() => {
        const handleFullscreenChange = () => {
            if (!document.fullscreenElement) {
                addViolation("You must remain in fullscreen mode during the exam.");
                setViolationMessage(
                    "You must remain in fullscreen mode during the exam. We kindly request your cooperation in maintaining exam integrity. Any suspicious activity, including attempts to bypass restrictions, will be recorded by our system and reviewed by the examination board. Repeated violations may lead to disqualification. Thank you for adhering to the rules."
                );
                setShowWarning(true);
                enterFullScreen();
            }
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
    }, []);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.ctrlKey && e.shiftKey && (e.key === "I" || e.key === "J" || e.key === "C")) {
                e.preventDefault();
                addViolation("Developer tools are disabled during the exam.");
                setViolationMessage(
                    "Developer tools are disabled during the exam. We kindly request your cooperation in maintaining exam integrity. Any suspicious activity, including attempts to bypass restrictions, will be recorded by our system and reviewed by the examination board. Repeated violations may lead to disqualification. Thank you for adhering to the rules."
                );
                setShowWarning(true);
            }
        };
        document.addEventListener("keydown", handleKeyDown);
        return () => document.removeEventListener("keydown", handleKeyDown);
    }, []);

    const enterFullScreen = async () => {
        try {
            const elem = document.documentElement;
            if (elem.requestFullscreen) {
                await elem.requestFullscreen();
            } else if (elem.webkitRequestFullscreen) {
                await elem.webkitRequestFullscreen();
            }
            setIsFullscreen(true);
        } catch (err) {
            console.error("Fullscreen request failed:", err);
        }
    };

    const handleAgree = () => {
        enterFullScreen();
        setShowWarning(false);
    };

    useEffect(() => {
        const fetchExamQuestions = async () => {
            try {
                setLoading(true);
                const currentUser = auth.currentUser;
                if (!currentUser) {
                    throw new Error("User not logged in");
                }

                const idToken = await currentUser.getIdToken();

                const response = await api.get(`${API_BASE_URL}/exams/${examId}/with-questions`, {
                    headers: {
                        Authorization: `Bearer ${idToken}`,
                    },
                });

                setQuestions(response.data.questions || []);
            } catch (err) {
                console.error("Error fetching exam questions:", err);
                setError(err instanceof Error ? err.message : "Failed to fetch exam questions");
            } finally {
                setLoading(false);
            }
        };

        fetchExamQuestions();
    }, [examId]);

    useEffect(() => {
        if (timeLeft > 0) {
            const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
            return () => clearInterval(timer);
        } else {
            handleFinishExam();
        }
    }, [timeLeft]);

    const handleFinishExam = async () => {
        if (isSubmitting) {
            return; // Prevent multiple submissions
        }

        setIsSubmitting(true); // Disable further submissions
        setError(null); // Clear any previous errors

        try {
            const currentUser = auth.currentUser;
            if (!currentUser) {
                throw new Error("User not logged in"); // Ensure user is authenticated
            }

            const studentId = currentUser.uid;
            const generatedAttemptId = attemptId || uuidv4(); // Generate or reuse attemptId
            setAttemptId(generatedAttemptId);

            // Calculate results 
            let correctAnswers = 0;
            const totalAnswered = Object.keys(selectedAnswers).length;

            const answersWithQuestions = questions.map((question, index) => {
                const isCorrect = selectedAnswers[index] === question.correctAnswer;
                if (isCorrect) correctAnswers++;

                return {
                    answerId: uuidv4(),
                    attemptId: generatedAttemptId,
                    studentId,
                    examId,
                    questionId: question.questionId,
                    selectedAnswer: selectedAnswers[index] || "",
                    isCorrect,
                    marksObtained: isCorrect ? question.mark : 0,
                    timeTaken: 0,
                    submittedAt: new Date().toISOString(),
                    question,
                };
            });

            // New logic: Prepare mistakes for incorrect answers
            const mistakes = answersWithQuestions
                .filter((answer) => !answer.isCorrect && answer.selectedAnswer !== "") // Only incorrect and answered questions
                .map((answer) => ({
                    mistakeId: uuidv4(),
                    studentId,
                    examId,
                    questionId: answer.questionId,
                    incorrectAnswer: answer.selectedAnswer,
                    correctAnswer: answer.question.correctAnswer,
                    mistakeType: "INCORRECT_SELECTION", // Default type (customize as needed)
                    reviewed: false, // Default to unreviewed
                    timestamp: new Date().toISOString(),
                    reviewComments: "", // Empty by default 
                }));

            const incorrect = totalAnswered - correctAnswers;
            const unattempted = questions.length - totalAnswered;
            const accuracy = totalAnswered > 0 ? (correctAnswers / totalAnswered) * 100 : 0;

            const examAttemptData = {
                attemptId: generatedAttemptId,
                studentId,
                examId,
                score: correctAnswers,
                percentile: 0,
                accuracy,
                correct: correctAnswers,
                incorrect,
                unattempted,
                status: "COMPLETED",
                attemptDate: new Date().toISOString(),
                retakeCount: 0,
                startTime: startTime.toISOString(),
                endTime: new Date().toISOString(),
                totalTimeSpent: duration - timeLeft,
            };

            const idToken = await currentUser.getIdToken();
            const headers = {
                Authorization: `Bearer ${idToken}`,
                "Content-Type": "application/json",
            };

            // API Call 1: Submit exam attempt 
            await api.post(`${API_BASE_URL}/student-exams`, examAttemptData, { headers });

            // API Call 2: Submit individual answers
            await api.post(`${API_BASE_URL}/student-answers/multiple`, answersWithQuestions, { headers });

            // API Call 3: Submit mistakes
            if (mistakes.length > 0) {
                await api.post(`${API_BASE_URL}/mistakes/multiple`, mistakes, { headers });
            }

            // Navigate to results page
            navigate("/result", {
                state: {
                    attemptId: generatedAttemptId,
                    results: answersWithQuestions,
                    examAttemptData,
                },
                replace: true,
            });
        } catch (err) {
            console.error("Error submitting exam:", err);
            setError(err instanceof Error ? err.message : "Failed to submit exam");
            setIsSubmitting(false); // Re-enable submission on error
        }
    };

    const handleOptionSelect = (option: string) => {
        setSelectedAnswers((prev) => ({
            ...prev,
            [currentQuestionIndex]: option,
        }));
    };

    const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex((prev) => prev - 1);
        }
    };

    const handleNext = () => {
        if (currentQuestionIndex < questions.length - 1) {
            setCurrentQuestionIndex((prev) => prev + 1);
        }
    };

    const handleMarkForReview = () => {
        setReviewLater((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(currentQuestionIndex)) {
                newSet.delete(currentQuestionIndex);
            } else {
                newSet.add(currentQuestionIndex);
            }
            return newSet;
        });
    };

    const getQuestionStatus = (index: number): "answered" | "notAttempted" | "reviewLater" => {
        if (reviewLater.has(index)) return "reviewLater";
        if (selectedAnswers[index]) return "answered";
        return "notAttempted";
    };

    // New function to handle report submission
    const submitReport = async (reportType: string, message: string) => {
        try {
            const currentUser = auth.currentUser;
            if (!currentUser) {
                throw new Error("User not logged in");
            }
            const idToken = await currentUser.getIdToken();

            const reportData = {
                questionId: currentQuestion.questionId,
                reportType,
                message,
            };

            await api.post(`${API_BASE_URL}/reported-questions`, reportData, {
                headers: {
                    Authorization: `Bearer ${idToken}`,
                    "Content-Type": "application/json",
                },
            });
        } catch (err) {
            console.error("Error submitting report:", err);
            throw err; // Let the modal handle the error
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-12">
                <p className="text-red-600">Error: {error}</p>
                <button
                    onClick={() => handleFinishExam()}
                    className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? "Submitting..." : "Retry Submit Exam"}
                </button>
            </div>
        );
    }

    if (!currentQuestion) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-600">No questions available.</p>
            </div>
        );
    }

    return (
        <>
            {showWarning && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="relative bg-white rounded-lg shadow-xl p-6 max-w-sm w-full mx-4 transform transition-all duration-300 scale-100">
                        <div className="text-center">
                            <h3 className="text-lg font-semibold text-red-500 mb-2">Important Notice!</h3>
                            <p className="text-sm text-gray-600 mb-4">{violationMessage}</p>
                            <button
                                onClick={handleAgree}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                            >
                                I Understand
                            </button>
                        </div>
                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2">
                            <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white"></div>
                        </div>
                    </div>
                </div>
            )}
            <div className="min-h-screen bg-gray-50 flex flex-col">
                <AppBar
                    timeLeft={`${Math.floor(timeLeft / 60)}:${(timeLeft % 60).toString().padStart(2, "0")}`}
                    currentQuestion={currentQuestionIndex + 1}
                    totalQuestions={questions.length}
                    questionDuration={currentQuestion?.duration || 0}
                    // Adding report button props
                    reportButton={
                        <button
                            onClick={() => setIsReportModalOpen(true)}
                            className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        >
                            <AlertTriangle className="w-5 h-5" />
                            <span>Report</span>
                        </button>
                    }
                />

                <div style={chatboxStyle}>
                    <div
                        className="bg-blue-100 p-4 border-b flex justify-between items-center cursor-pointer"
                        onClick={() => setIsChatExpanded(!isChatExpanded)}
                    >
                        <div>
                            <h3 className="font-semibold">{isChatExpanded ? "Exam Warnings" : "Warnings"}</h3>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm animate-pulse">
                                {violations.length}
                            </div>
                            <span className="text-gray-600 text-sm">{isChatExpanded ? "▼" : "▲"}</span>
                        </div>
                    </div>
                    {isChatExpanded && (
                        <div className="p-4 h-64 overflow-y-auto">
                            {violations.length === 0 ? (
                                <p className="text-gray-500 text-sm">No violations detected yet</p>
                            ) : (
                                violations.map((violation, index) => (
                                    <div key={index} className="mb-3 text-sm">
                                        <div className="text-gray-500 text-xs">
                                            {violation.timestamp.toLocaleTimeString()}
                                        </div>
                                        <div className="text-red-600">{violation.message}</div>
                                    </div>
                                ))
                            )}
                        </div>
                    )}
                </div>

                <main className="flex-grow overflow-y-auto pt-16 pb-40">
                    <div className="container mx-auto p-4">
                        {!isFullscreen && (
                            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                <div className="bg-white p-6 rounded-lg shadow-xl text-center">
                                    <h2 className="text-xl font-semibold mb-4">Start Exam</h2>
                                    <p className="mb-4">This exam requires fullscreen mode.</p>
                                    <button
                                        onClick={enterFullScreen}
                                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                    >
                                        Enter Fullscreen & Start Exam
                                    </button>
                                </div>
                            </div>
                        )}

                        <QuestionCard question={currentQuestion.question} />
                        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            {currentQuestion.options.map((option) => (
                                <AnswerOption
                                    key={option}
                                    option={option}
                                    isSelected={selectedAnswers[currentQuestionIndex] === option}
                                    onSelect={() => handleOptionSelect(option)}
                                />
                            ))}
                        </div>
                        <div className="mt-6 flex justify-between items-center">
                            <button
                                onClick={handlePrevious}
                                disabled={currentQuestionIndex === 0}
                                className="px-4 py-2 bg-gray-600 text-white rounded disabled:bg-gray-300 hover:bg-gray-700"
                            >
                                Previous
                            </button>
                            <button
                                onClick={handleMarkForReview}
                                className={`px-4 py-2 rounded ${reviewLater.has(currentQuestionIndex)
                                    ? "bg-yellow-500 text-white hover:bg-yellow-600"
                                    : "bg-yellow-300 hover:bg-yellow-400"
                                    }`}
                            >
                                {reviewLater.has(currentQuestionIndex) ? "Unmark Review" : "Mark for Review"}
                            </button>
                            <button
                                onClick={handleNext}
                                disabled={currentQuestionIndex === questions.length - 1}
                                className="px-4 py-2 bg-gray-600 text-white rounded disabled:bg-gray-300 hover:bg-gray-700"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                </main>

                <div className="fixed bottom-0 left-0 right-0 z-10 bg-white border-t shadow-lg">
                    <QuestionNavigatorModal
                        questions={questions.map((_, index) => ({
                            status: getQuestionStatus(index),
                        }))}
                        currentIndex={currentQuestionIndex}
                        onJumpToQuestion={(index) => {
                            setCurrentQuestionIndex(index);
                        }}
                        onFinishExam={handleFinishExam}
                    />
                </div>

                {showChatbox && (
                    <div className="fixed bottom-4 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-4 bg-gray-100 rounded-t-lg">
                            <h3 className="font-semibold">Violation Warnings</h3>
                        </div>
                        <div className="p-4 max-h-64 overflow-y-auto">
                            {violations.map((violation, index) => (
                                <div key={index} className="mb-2 text-sm text-gray-700">
                                    <span className="font-medium">{violation.timestamp.toLocaleTimeString()}:</span>{" "}
                                    {violation.message}
                                </div>
                            ))}
                            {violations.length === 0 && (
                                <p className="text-sm text-gray-500">No violations detected.</p>
                            )}
                        </div>
                    </div>
                )}

                {/* Add the ReportQuestionModal */}
                <ReportQuestionModal
                    isOpen={isReportModalOpen}
                    onClose={() => setIsReportModalOpen(false)}
                    questionId={currentQuestion.questionId}
                    onReport={submitReport}
                />
            </div>
        </>
    );
};

export default ExamOverviewPage;