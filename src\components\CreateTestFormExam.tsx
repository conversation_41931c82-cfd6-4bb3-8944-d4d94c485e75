import React, { useState, useEffect } from 'react';
import { auth } from '../firebase-config';
import { X, Calendar, Clock, BookOpen, FileText, Settings, Tag, AlertCircle, List, Trash2, Loader2 } from 'lucide-react';
import ErrorPopupModal from './ErrorPopupModal'; // Assuming this is the path
import api from '../axios';
import axios from 'axios';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface SyllabusEntry {
  categoryId: string;
  questionCount: number;
  marks: number;
  difficultyLevel: 'EASY' | 'MEDIUM' | 'HARD';
  instructions: string;
}

interface ExamCategory {
  examCategoryId: string;
  name: string;
  description: string;
}

interface SyllabusCategory {
  categoryId: string;
  categoryName: string;
  totalQuestions: number;
  questionsByDifficulty: {
    EASY: number;
    MEDIUM: number;
    HARD: number;
  };
}

const CreateTestFormExam: React.FC<Props> = ({ isOpen, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    examName: '',
    examDate: '',
    passingMarks: 0,
    totalMarks: 0,
    totalQuestions: 0,
    duration: 0,
    status: 'DRAFT',
    instructions: '',
    description: '',
    examType: 'PRACTICE',
    examCategoryId: '',
    syllabusCategories: [] as SyllabusEntry[],
  });
  const [examCategories, setExamCategories] = useState<ExamCategory[]>([]);
  const [syllabusCategories, setSyllabusCategories] = useState<SyllabusCategory[]>([]);
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccessPopupOpen, setIsSuccessPopupOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [warningPopup, setWarningPopup] = useState<string | null>(null);

  const fetchExamCategories = async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error('User not logged in');
      const idToken = await currentUser.getIdToken();
      const response = await api.get('/api/exam-categories', {
        headers: {
          'Accept': '*/*',
          'Authorization': `Bearer ${idToken}`,
        },
      });
      const data = response.data;;
      setExamCategories(data);
      if (data.length > 0) setFormData((prev) => ({ ...prev, examCategoryId: data[0].examCategoryId }));
    } catch (error) {
      console.error('Error fetching exam categories:', error);
    }
  };

  const fetchSyllabusCategories = async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error('User not logged in');
      const idToken = await currentUser.getIdToken();
      const response = await api.get('/api/questions/stats', {
        headers: {
          'Accept': '*/*',
          'Authorization': `Bearer ${idToken}`,
        },
      });
      const data = response.data;;
      setSyllabusCategories(data);
    } catch (error) {
      console.error('Error fetching syllabus categories:', error);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchExamCategories();
      fetchSyllabusCategories();
      setFormData({
        examName: '',
        examDate: '',
        passingMarks: 0,
        totalMarks: 0,
        totalQuestions: 0,
        duration: 0,
        status: 'DRAFT',
        instructions: '',
        description: '',
        examType: 'PRACTICE',
        examCategoryId: examCategories[0]?.examCategoryId || '',
        syllabusCategories: [],
      });
      setError(null);
      setSuccessMessage(null);
      setIsSuccessPopupOpen(false);
      setWarningPopup(null);
    }
  }, [isOpen]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: ['passingMarks', 'totalMarks', 'totalQuestions', 'duration'].includes(name)
        ? Number(value) || 0
        : value,
    }));
  };

  const handleAddSyllabusRow = () => {
    setFormData((prev) => ({
      ...prev,
      syllabusCategories: [
        ...prev.syllabusCategories,
        {
          categoryId: syllabusCategories[0]?.categoryId || '',
          questionCount: 0,
          marks: 0,
          difficultyLevel: 'EASY',
          instructions: '',
        },
      ],
    }));
  };

  const handleDeleteSyllabusRow = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      syllabusCategories: prev.syllabusCategories.filter((_, i) => i !== index),
    }));
  };

  const handleSyllabusChange = (
    index: number,
    field: keyof SyllabusEntry,
    value: string | number
  ) => {
    const updatedValue = field === 'categoryId' || field === 'difficultyLevel' ? value : Number(value) || 0;
    setFormData((prev) => {
      const updatedSyllabus = [...prev.syllabusCategories];
      updatedSyllabus[index] = {
        ...updatedSyllabus[index],
        [field]: updatedValue,
      } as SyllabusEntry;

      // Validate question count against available questions
      if (field === 'questionCount' || field === 'difficultyLevel') {
        const category = syllabusCategories.find(cat => cat.categoryId === updatedSyllabus[index].categoryId);
        if (category) {
          const availableQuestions = category.questionsByDifficulty[updatedSyllabus[index].difficultyLevel];
          if (Number(updatedSyllabus[index].questionCount) > availableQuestions) {
            setWarningPopup(
              `Category "${category.categoryName}" has only ${availableQuestions} ${updatedSyllabus[index].difficultyLevel} questions available. Total questions: ${category.totalQuestions} (EASY: ${category.questionsByDifficulty.EASY}, MEDIUM: ${category.questionsByDifficulty.MEDIUM}, HARD: ${category.questionsByDifficulty.HARD}).`
            );
            updatedSyllabus[index].questionCount = availableQuestions; // Cap at maximum available
          }
        }
      }

      return { ...prev, syllabusCategories: updatedSyllabus };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error('User not logged in');
      const idToken = await currentUser.getIdToken();

      if (!formData.examName.trim()) throw new Error('Exam name is required');
      if (!formData.examDate) throw new Error('Exam date is required');
      if (!formData.examCategoryId) throw new Error('Exam category is required');
      if (formData.totalMarks < formData.passingMarks)
        throw new Error('Total marks must be greater than or equal to passing marks');
      if (formData.syllabusCategories.length === 0)
        throw new Error('At least one syllabus entry is required');

      const totalQuestionsInSyllabus = formData.syllabusCategories.reduce(
        (sum, entry) => sum + entry.questionCount,
        0
      );
      const totalMarksInSyllabus = formData.syllabusCategories.reduce(
        (sum, entry) => sum + entry.marks,
        0
      );
      if (totalQuestionsInSyllabus !== formData.totalQuestions)
        throw new Error(
          `Total questions in syllabus (${totalQuestionsInSyllabus}) must equal total questions (${formData.totalQuestions})`
        );
      if (totalMarksInSyllabus !== formData.totalMarks)
        throw new Error(
          `Total marks in syllabus (${totalMarksInSyllabus}) must equal total marks (${formData.totalMarks})`
        );

      // Additional validation against available questions
      for (const entry of formData.syllabusCategories) {
        const category = syllabusCategories.find(cat => cat.categoryId === entry.categoryId);
        if (category) {
          const availableQuestions = category.questionsByDifficulty[entry.difficultyLevel];
          if (entry.questionCount > availableQuestions) {
            throw new Error(
              `Category "${category.categoryName}" has only ${availableQuestions} ${entry.difficultyLevel} questions available, but ${entry.questionCount} were requested.`
            );
          }
        }
      }

      const payload = {
        examName: formData.examName,
        examDate: new Date(formData.examDate).toISOString(),
        passingMarks: formData.passingMarks,
        totalMarks: formData.totalMarks,
        totalQuestions: formData.totalQuestions,
        duration: formData.duration,
        status: formData.status,
        instructions: formData.instructions,
        description: formData.description,
        examType: formData.examType,
        examCategoryId: formData.examCategoryId,
        syllabusCategories: formData.syllabusCategories.map((entry) => ({
          categoryId: entry.categoryId,
          marks: entry.marks,
          questionCount: entry.questionCount,
          difficultyLevel: entry.difficultyLevel,
          instructions: entry.instructions || '',
        })),
      };

      try {
        const response = await api.post('/api/exams', payload, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`,
          },
        });

        const newExam = response.data; // Axios automatically parses the response data

        setSuccessMessage(`Exam "${newExam.examName}" created successfully! ${newExam.totalQuestions} questions have been automatically added by the backend.`);
        setIsSuccessPopupOpen(true);
        onSuccess();

      } catch (error) {
        if (axios.isAxiosError(error)) {
          const errorMessage = error.response?.data || 'Server error occurred';
          throw new Error(`Failed to create exam: ${errorMessage}`);
        } else {
          throw new Error('An unexpected error occurred');
        }
      }

    } catch (error) {
      console.error('Error creating exam:', error);
      setError(error instanceof Error ? error.message : 'Failed to create exam');
    } finally {
      setIsLoading(false);
    }
  };

  const steps = [
    { number: 1, title: 'Basic Info', icon: FileText },
    { number: 2, title: 'Settings', icon: Settings },
    { number: 3, title: 'Details', icon: AlertCircle },
    { number: 4, title: 'Syllabus', icon: List },
  ];

  const totalQuestionCount = formData.syllabusCategories.reduce(
    (sum, entry) => sum + entry.questionCount,
    0
  );
  const totalMarks = formData.syllabusCategories.reduce(
    (sum, entry) => sum + entry.marks,
    0
  );

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between bg-gray-50">
            <div className="flex items-center space-x-3">
              <BookOpen className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Create New Exam</h2>
            </div>
            <div className="flex items-center space-x-3">
              {currentStep === steps.length && (
                <button
                  type="submit"
                  form="examForm"
                  className={`px-6 py-2 ${isLoading ? 'bg-blue-500' : 'bg-blue-600'} text-white text-sm font-medium rounded-lg hover:${isLoading ? 'bg-blue-500' : 'bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors`}
                  disabled={isLoading}
                >
                  {isLoading ? <Loader2 className="h-5 w-5 animate-spin" /> : 'Create Exam'}
                </button>
              )}
              <button onClick={onClose} className="text-gray-400 hover:text-gray-500 transition-colors">
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 bg-white border-b border-gray-200">
            <div className="flex justify-between">
              {steps.map((step) => {
                const StepIcon = step.icon;
                return (
                  <div
                    key={step.number}
                    className={`flex items-center ${step.number < currentStep
                      ? 'text-blue-600'
                      : step.number === currentStep
                        ? 'text-blue-600'
                        : 'text-gray-400'
                      }`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${step.number <= currentStep
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-300 bg-white'
                          }`}
                      >
                        <StepIcon className="h-4 w-4" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium">{step.title}</p>
                      </div>
                    </div>
                    {step.number < steps.length && (
                      <div
                        className={`flex-1 h-0.5 mx-4 ${step.number < currentStep ? 'bg-blue-600' : 'bg-gray-300'}`}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <form id="examForm" onSubmit={handleSubmit} className="overflow-y-auto max-h-[calc(90vh-200px)]">
            <div className="px-6 py-4 space-y-6">
              {error && (
                <div className="p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  {error}
                </div>
              )}

              {currentStep === 1 && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Exam Name</label>
                    <input
                      type="text"
                      name="examName"
                      value={formData.examName}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                      placeholder="Enter exam name"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span>Exam Date</span>
                        </div>
                      </label>
                      <input
                        type="datetime-local"
                        name="examDate"
                        value={formData.examDate}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>Duration (minutes)</span>
                        </div>
                      </label>
                      <input
                        type="number"
                        name="duration"
                        value={formData.duration}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        min="1"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <div className="flex items-center space-x-2">
                        <Tag className="h-4 w-4 text-gray-400" />
                        <span>Exam Category</span>
                      </div>
                    </label>
                    <select
                      name="examCategoryId"
                      value={formData.examCategoryId}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                      required
                    >
                      {examCategories.length === 0 ? (
                        <option value="">Loading exam categories...</option>
                      ) : (
                        examCategories.map((category) => (
                          <option key={category.examCategoryId} value={category.examCategoryId}>
                            {category.name}
                          </option>
                        ))
                      )}
                    </select>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Passing Marks</label>
                      <input
                        type="number"
                        name="passingMarks"
                        value={formData.passingMarks}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        min="0"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Total Marks</label>
                      <input
                        type="number"
                        name="totalMarks"
                        value={formData.totalMarks}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        min="1"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Total Questions</label>
                    <input
                      type="number"
                      name="totalQuestions"
                      value={formData.totalQuestions}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                      min="1"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                      <select
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        required
                      >
                        <option value="DRAFT">Draft</option>
                        <option value="ACTIVE">Active</option>
                        <option value="COMPLETED">Completed</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Exam Type</label>
                      <select
                        name="examType"
                        value={formData.examType}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                        required
                      >
                        <option value="PRACTICE">Practice</option>
                        <option value="MOCK">Mock</option>
                        <option value="FINAL">Final</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                    <textarea
                      name="instructions"
                      value={formData.instructions}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                      rows={4}
                      placeholder="Enter exam instructions..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                      rows={4}
                      placeholder="Enter exam description..."
                    />
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <div className="flex items-center space-x-2">
                        <List className="h-4 w-4 text-gray-400" />
                        <span>Syllabus Configuration</span>
                      </div>
                    </label>
                    <div className="border border-gray-300 rounded-lg overflow-hidden">
                      <table className="w-full text-sm text-left text-gray-700">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 font-medium">Number</th>
                            <th className="px-4 py-2 font-medium">Category Section</th>
                            <th className="px-4 py-2 font-medium">Question Count</th>
                            <th className="px-4 py-2 font-medium">Marks</th>
                            <th className="px-4 py-2 font-medium">Difficulty Level</th>
                            <th className="px-4 py-2 font-medium">Instructions</th>
                            <th className="px-4 py-2 font-medium">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.syllabusCategories.length === 0 ? (
                            <tr>
                              <td colSpan={7} className="px-4 py-2 text-center text-gray-500">
                                No syllabus entries added yet. Click "Add Row" to start.
                              </td>
                            </tr>
                          ) : (
                            formData.syllabusCategories.map((entry, index) => (
                              <tr key={index} className="border-t">
                                <td className="px-4 py-2">{index + 1}</td>
                                <td className="px-4 py-2">
                                  <select
                                    value={entry.categoryId}
                                    onChange={(e) =>
                                      handleSyllabusChange(index, 'categoryId', e.target.value)
                                    }
                                    className="w-full px-2 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  >
                                    {syllabusCategories.length === 0 ? (
                                      <option value="">Loading categories...</option>
                                    ) : (
                                      syllabusCategories.map((category) => (
                                        <option key={category.categoryId} value={category.categoryId}>
                                          {category.categoryName}
                                        </option>
                                      ))
                                    )}
                                  </select>
                                </td>
                                <td className="px-4 py-2">
                                  <input
                                    type="number"
                                    value={entry.questionCount}
                                    onChange={(e) =>
                                      handleSyllabusChange(index, 'questionCount', e.target.value)
                                    }
                                    className="w-full px-2 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    min="0"
                                  />
                                </td>
                                <td className="px-4 py-2">
                                  <input
                                    type="number"
                                    value={entry.marks}
                                    onChange={(e) =>
                                      handleSyllabusChange(index, 'marks', e.target.value)
                                    }
                                    className="w-full px-2 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    min="0"
                                  />
                                </td>
                                <td className="px-4 py-2">
                                  <select
                                    value={entry.difficultyLevel}
                                    onChange={(e) =>
                                      handleSyllabusChange(index, 'difficultyLevel', e.target.value)
                                    }
                                    className="w-full px-2 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  >
                                    <option value="EASY">Easy</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HARD">Hard</option>
                                  </select>
                                </td>
                                <td className="px-4 py-2">
                                  <input
                                    type="text"
                                    value={entry.instructions}
                                    onChange={(e) =>
                                      handleSyllabusChange(index, 'instructions', e.target.value)
                                    }
                                    className="w-full px-2 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Enter instructions..."
                                  />
                                </td>
                                <td className="px-4 py-2">
                                  <button
                                    type="button"
                                    onClick={() => handleDeleteSyllabusRow(index)}
                                    className="text-red-600 hover:text-red-800"
                                    disabled={formData.syllabusCategories.length === 1}
                                  >
                                    <Trash2 className="h-5 w-5" />
                                  </button>
                                </td>
                              </tr>
                            ))
                          )}
                          <tr className="bg-gray-100 border-t font-medium">
                            <td className="px-4 py-2">Total</td>
                            <td className="px-4 py-2"></td>
                            <td className="px-4 py-2">{totalQuestionCount}</td>
                            <td className="px-4 py-2">{totalMarks}</td>
                            <td className="px-4 py-2"></td>
                            <td className="px-4 py-2"></td>
                            <td className="px-4 py-2"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <button
                      type="button"
                      onClick={handleAddSyllabusRow}
                      className="mt-2 inline-flex items-center px-3 py-1 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <List className="h-4 w-4 mr-2" />
                      Add Row
                    </button>
                    <p className="text-sm text-gray-500 mt-1">
                      Ensure the total question count matches {formData.totalQuestions} and total marks match{' '}
                      {formData.totalMarks}.
                    </p>
                  </div>
                </div>
              )}
            </div>

            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
              <button
                type="button"
                onClick={() => setCurrentStep((prev) => Math.max(1, prev - 1))}
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none"
                disabled={currentStep === 1}
              >
                Previous
              </button>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none"
                >
                  Cancel
                </button>
                {currentStep < steps.length && (
                  <button
                    type="button"
                    onClick={() => setCurrentStep((prev) => Math.min(steps.length, prev + 1))}
                    className="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                    Next
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Success Popup */}
      <ErrorPopupModal
        isOpen={isSuccessPopupOpen}
        message={successMessage || 'Exam created successfully!'}
        onClose={() => {
          setIsSuccessPopupOpen(false);
          onClose();
        }}
        onOk={() => {
          setIsSuccessPopupOpen(false);
          onClose();
        }}
      />

      {/* Warning Popup */}
      <ErrorPopupModal
        isOpen={!!warningPopup}
        message={warningPopup || ''}
        onClose={() => setWarningPopup(null)}
        onOk={() => setWarningPopup(null)}
      />
    </>
  );
};

export default CreateTestFormExam;