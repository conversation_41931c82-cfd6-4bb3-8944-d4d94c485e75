import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Header from "../components/Header";
import ResultSummary from "../components/ResultSummary";
import QuestionGrid from "../components/QuestionGrid";
import FeedbackModal from "../components/FeedbackModal";
import { auth } from '../firebase-config';
import axios from 'axios';
import api from "../axios";

const API_BASE_URL = '/api';

interface AnswerResult {
    answerId: string;
    attemptId: string;
    studentId: string;
    examId: string;
    questionId: string;
    selectedAnswer: string;
    isCorrect: boolean;
    marksObtained: number;
    timeTaken: number;
    submittedAt: string;
    question?: QuestionType;
}

interface QuestionType {
    questionId: string;
    question: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
    mark: number;
    negativeMark: number;
    duration: number;
    difficultyLevel: string;
    questionType: string;
    categoryId: string;
    topicId: string;
    images: string[];
    tags: string[];
    createdAt: string;
    updatedAt: string;
}

interface ExamAttemptData {
    attemptId: string;
    studentId: string;
    examId: string;
    score: number;
    percentile: number;
    accuracy: number;
    correct: number;
    incorrect: number;
    unattempted: number;
    status: string;
    attemptDate: string;
    retakeCount: number;
    startTime: string;
    endTime: string;
    totalTimeSpent: number;
}

interface ResultForDisplay {
    question: string;
    yourAnswer: string;
    correctAnswer: string;
    explanation: string;
    status: "correct" | "incorrect" | "unattempted";
    questionIndex: number;
    mark: number;
    negativeMark: number;
}

const ExamResultScreen: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [selectedQuestionIndex, setSelectedQuestionIndex] = useState<number | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [results, setResults] = useState<ResultForDisplay[]>([]);
    const [stats, setStats] = useState({
        correctAnswers: 0,
        wrongAnswers: 0,
        unattempted: 0,
        totalMarks: 0,
        obtainedMarks: 0,
        accuracy: 0
    });



    const studentAnswerList = location.state?.results as AnswerResult[];
    const attemptId = location.state?.attemptId as string;
    const examAttemptData = location.state?.examAttemptData as ExamAttemptData;

    useEffect(() => {
        if (!studentAnswerList || !attemptId) {
            console.error("Missing required data in location state");
            navigate("/dashboard");
            return;
        }

        // Process results
        const processedResults = studentAnswerList.map((answer, index): ResultForDisplay => {
            const status = answer.selectedAnswer
                ? (answer.isCorrect ? "correct" : "incorrect")
                : "unattempted";

            return {
                question: answer.question?.question || "Question not available",
                yourAnswer: answer.selectedAnswer || "Not attempted",
                correctAnswer: answer.question?.correctAnswer || "Not available",
                explanation: answer.question?.explanation || "No explanation available",
                status,
                questionIndex: index,
                mark: answer.question?.mark || 0,
                negativeMark: answer.question?.negativeMark || 0
            };
        });

        // Calculate statistics
        const correctAnswers = processedResults.filter(r => r.status === "correct").length;
        const wrongAnswers = processedResults.filter(r => r.status === "incorrect").length;
        const unattempted = processedResults.filter(r => r.status === "unattempted").length;
        const totalMarks = processedResults.reduce((sum, r) => sum + r.mark, 0);
        const obtainedMarks = processedResults.reduce((sum, r) => {
            if (r.status === "correct") return sum + r.mark;
            if (r.status === "incorrect") return sum - r.negativeMark;
            return sum;
        }, 0);
        const accuracy = (correctAnswers / (correctAnswers + wrongAnswers)) * 100 || 0;

        setResults(processedResults);
        setStats({
            correctAnswers,
            wrongAnswers,
            unattempted,
            totalMarks,
            obtainedMarks,
            accuracy
        });

        const verifyResults = async () => {
            try {
                setIsLoading(true);
                const currentUser = auth.currentUser;
                if (!currentUser) {
                    throw new Error("User not logged in");
                }

                const idToken = await currentUser.getIdToken();

                const attemptResponse = await api.get(
                    `${API_BASE_URL}/student-exams/${attemptId}`,
                    {
                        headers: {
                            'Authorization': `Bearer ${idToken}`
                        }
                    }
                );

                setIsLoading(false);
            } catch (err) {
                console.error("Error verifying results:", err);
                setIsLoading(false);
            }
        };

        verifyResults();
    }, [attemptId, studentAnswerList, navigate]);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading results...</p>
                </div>
            </div>
        );
    }

    if (!results.length) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-600 mb-4">No result data available</p>
                    <button
                        onClick={() => navigate("/dashboard")}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Return to Dashboard
                    </button>
                </div>
            </div>
        );
    }

    const handleQuestionClick = (index: number) => {
        setSelectedQuestionIndex(index);
    };

    const handleModalClose = () => {
        setSelectedQuestionIndex(null);
    };

    const handleTryAgain = () => {
        navigate(`/exams-start/${studentAnswerList[0]?.examId}`);
    };

    const handleGoToHome = () => {
        navigate("/dashboard");
    };

    return (
        <div className="min-h-screen bg-blue-100">
            <Header correctAnswers={stats.correctAnswers} totalQuestions={results.length} />

            <ResultSummary
                correctAnswers={stats.correctAnswers}
                wrongAnswers={stats.wrongAnswers}
                unattempted={stats.unattempted}
                totalQuestions={results.length}
                accuracy={stats.accuracy}
                totalMarks={stats.totalMarks}
                obtainedMarks={stats.obtainedMarks}
            />

            {error && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 mx-4">
                    <p className="text-yellow-700">
                        Note: {error}
                    </p>
                </div>
            )}

            <div className="px-4 py-6">
                <div className="grid grid-cols-3 gap-4 mb-8 text-center">
                    <div className="bg-green-100 p-4 rounded-lg">
                        <p className="text-green-800 text-lg font-semibold">{stats.correctAnswers}</p>
                        <p className="text-green-600">Correct</p>
                    </div>
                    <div className="bg-red-100 p-4 rounded-lg">
                        <p className="text-red-800 text-lg font-semibold">{stats.wrongAnswers}</p>
                        <p className="text-red-600">Wrong</p>
                    </div>
                    <div className="bg-gray-100 p-4 rounded-lg">
                        <p className="text-gray-800 text-lg font-semibold">{stats.unattempted}</p>
                        <p className="text-gray-600">Not Attempted</p>
                    </div>
                </div>
            </div>

            <QuestionGrid
                questions={results}
                onQuestionClick={handleQuestionClick}
            />

            {selectedQuestionIndex !== null && (
                <FeedbackModal
                    question={results[selectedQuestionIndex].question}
                    yourAnswer={results[selectedQuestionIndex].yourAnswer}
                    correctAnswer={results[selectedQuestionIndex].correctAnswer}
                    explanation={results[selectedQuestionIndex].explanation}
                    onClose={handleModalClose}
                />
            )}

            <div className="mt-6 flex justify-between px-6 pb-8">
                <button
                    onClick={handleTryAgain}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                    Try Again
                </button>
                <button
                    onClick={handleGoToHome}
                    className="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                    Go to Dashboard
                </button>
            </div>
        </div>
    );
};

export default ExamResultScreen;