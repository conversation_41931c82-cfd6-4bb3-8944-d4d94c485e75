{"name": "exam-management-system", "private": true, "version": "0.0.0", "type": "module", "dev": "vite --port 4000", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.24.1", "axios": "^1.6.7", "clsx": "^2.1.0", "date-fns": "^3.3.1", "firebase": "^11.2.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "papaparse": "^5.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.50.1", "react-router-dom": "^6.22.1", "tailwind-merge": "^2.2.1", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}