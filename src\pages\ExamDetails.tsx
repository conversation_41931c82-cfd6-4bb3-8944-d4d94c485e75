import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from "react-router-dom";
import { Clock, Calendar, Award, AlertCircle, CheckCircle2, Loader2 } from 'lucide-react';
import { auth } from '../firebase-config';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';

interface ExamDetailsType {
  examId: string;
  examName: string;
  examDate: string | null;
  passingMarks: number;
  totalMarks: number | null;
  totalQuestions: number | null;
  duration: number | null;
  status: string;
  instructions: string;
  description: string;
  examType: string;
  examCategoryId: string;
  createdAt: string;
  updatedAt: string;
}

function ExamDetails() {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const [exam, setExam] = useState<ExamDetailsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      try {
        setLoading(true);
        setErrorMessage(null);

        if (!user) {
          throw new Error("User not logged in, Please change tab and try again.");
        }

        const idToken = await user.getIdToken();
        if (!idToken) {
          throw new Error("Failed to get user ID token");
        }

        const response = await api.get(
          `/api/exams/${examId}`,
          {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          }
        );


        const data = response.data;;
        setExam(data);
      } catch (err) {
        setErrorMessage(err instanceof Error ? err.message : 'Failed to fetch exam details');
        setIsErrorPopupOpen(true);
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [examId]);

  const handleStartExam = () => {
    if (exam) {
      navigate(`/exams-start/${exam.examId}`);
    }
  };

  const handleDownloadInstructions = () => {
    if (!exam) return;

    const content = `
      ExaMate an Online Exam Platform

Exam Details and Instructions
---------------------------
Exam Name: ${exam.examName}
Status: ${exam.status}
${exam.examDate ? `Date: ${new Date(exam.examDate).toLocaleDateString()}` : ''}
${exam.examDate ? `Time: ${new Date(exam.examDate).toLocaleTimeString()}` : ''}
${exam.totalMarks ? `Total Marks: ${exam.totalMarks}` : ''}
${exam.duration ? `Duration: ${exam.duration} minutes` : ''}
Passing Marks: ${exam.passingMarks}
Exam Type: ${exam.examType}

Description:
${exam.description}

Instructions:
${exam.instructions}

Additional Guidelines:
- Please read all instructions carefully before proceeding
- Ensure you have a stable internet connection
- Do not switch tabs or windows during the exam
- Timer will start automatically once you begin
- All questions are compulsory
- There is negative marking for wrong answers
`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${exam.examName}_Instructions.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {exam ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold text-gray-900">{exam.examName}</h1>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                {exam.status}
              </span>
            </div>
          </div>

          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {exam.examDate && (
                  <div className="flex items-center text-gray-700">
                    <Calendar className="h-5 w-5 mr-2" />
                    <span>Date: {new Date(exam.examDate).toLocaleDateString()}</span>
                  </div>
                )}
                {exam.examDate && (
                  <div className="flex items-center text-gray-700">
                    <Clock className="h-5 w-5 mr-2" />
                    <span>Time: {new Date(exam.examDate).toLocaleTimeString()}</span>
                  </div>
                )}
                {exam.totalMarks && (
                  <div className="flex items-center text-gray-700">
                    <Award className="h-5 w-5 mr-2" />
                    <span>Total Marks: {exam.totalMarks}</span>
                  </div>
                )}
              </div>
              <div className="space-y-4">
                {exam.duration && (
                  <div className="flex items-center text-gray-700">
                    <Clock className="h-5 w-5 mr-2" />
                    <span>Duration: {exam.duration} minutes</span>
                  </div>
                )}
                <div className="flex items-center text-gray-700">
                  <CheckCircle2 className="h-5 w-5 mr-2" />
                  <span>Passing Marks: ${exam.passingMarks}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <span>Exam Type: ${exam.examType}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="px-6 py-4 border-t border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Description</h2>
            <p className="text-gray-700">{exam.description}</p>
          </div>

          <div className="px-6 py-4 border-t border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Instructions</h2>
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Please read all instructions carefully before proceeding.
                  </p>
                  <p className="text-sm text-yellow-700">Ensure you have a stable internet connection.</p>
                  <p className="text-sm text-yellow-700">Do not switch tabs or windows during the exam.</p>
                  <p className="text-sm text-yellow-700">Timer will start automatically once you begin the exam.</p>
                  <p className="text-sm text-yellow-700">All questions are compulsory.</p>
                  <p className="text-sm text-yellow-700">There is negative marking for wrong answers.</p>
                </div>
              </div>
            </div>
            <ul className="list-disc list-inside space-y-2">
              {exam.instructions.split('\n').map((instruction, index) => (
                <li key={index} className="text-gray-700">{instruction}</li>
              ))}
            </ul>
          </div>

          <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-4">
            <button
              onClick={handleDownloadInstructions}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Download Instructions
            </button>
            <button
              onClick={handleStartExam}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Start Exam
            </button>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">Exam details not found.</div>
      )}

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => {
          setIsErrorPopupOpen(false);
        }}
      />
    </div>
  );
}

export default ExamDetails;