import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import {
  LayoutDashboard,
  User,
  Users,
  FileText,
  ClipboardCheck,
  Settings,
  Archive,
  ClipboardList,
  ChevronLeft,
  ChevronRight,
  Menu,
  Database
} from 'lucide-react';
import clsx from 'clsx';
import Footer from './Footer';

type NavItem = {
  name: string;
  href: string;
  icon: any;
};

const Layout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const navigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { name: 'Exams', href: '/exams', icon: FileText },
    { name: 'Practice', href: '/practice', icon: ClipboardCheck },
    { name: 'Exam Management', href: '/exams-management', icon: ClipboardList },
    { name: 'Student Management', href: '/user-management', icon: Users },
    { name: 'Question Bank', href: '/questions-management', icon: Archive },
    { name: 'Profile', href: '/profile', icon: User },
  ];

  if (user?.role === 'ADMIN' || user?.role === 'INSTRUCTOR') {
    navigation.push({ name: 'Saved Results', href: '/saved-results', icon: Database });
  }

  if (user?.role === 'ADMIN') {
    navigation.push({ name: 'Settings', href: '/settings', icon: Settings });
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        className={clsx(
          'fixed z-50 left-4 top-4 p-2 rounded-lg bg-indigo-600 text-white md:hidden',
          isSidebarOpen && 'hidden'
        )}
      >
        <Menu className="w-6 h-6" />
      </button>

      {/* Sidebar Backdrop for Mobile */}
      {isMobile && isSidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={clsx(
          'fixed md:relative z-50 h-full flex-shrink-0 bg-indigo-600 shadow-xl transition-all duration-300',
          isMobile ? 'w-64' : isSidebarOpen ? 'w-64' : 'w-20',
          isMobile
            ? isSidebarOpen
              ? 'translate-x-0'
              : '-translate-x-full'
            : 'translate-x-0'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-indigo-500">
            <Link to="/" className="flex items-center gap-2">
              <img
                className="h-10 w-10 rounded-lg object-cover"
                src="https://images.unsplash.com/photo-1497633762265-9d179a990aa6?w=400&h=400&fit=crop&auto=format"
                alt="ExaMate Logo"
              />
              {(!isMobile && isSidebarOpen) && (
                <span className="text-xl font-bold text-white">ExaMate</span>
              )}
            </Link>
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="hidden md:flex p-1.5 hover:bg-indigo-700 rounded-lg text-indigo-100"
            >
              {isSidebarOpen ? (
                <ChevronLeft className="w-5 h-5" />
              ) : (
                <ChevronRight className="w-5 h-5" />
              )}
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-2 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => isMobile && setIsSidebarOpen(false)}
                  className={clsx(
                    'flex items-center gap-3 p-3 mb-1 rounded-lg transition-colors',
                    isActive
                      ? 'bg-indigo-800 text-white font-semibold'
                      : 'text-indigo-100 hover:bg-indigo-700'
                  )}
                >
                  <item.icon className="w-6 h-6 flex-shrink-0" />
                  {isSidebarOpen && (
                    <span className="text-sm truncate">{item.name}</span>
                  )}

                </Link>
              );
            })}
          </nav>

          {/* User Profile */}
          <div className="border-t border-indigo-500 p-4">
            <button
              onClick={() => logout(navigate)}
              className="flex items-center gap-3 w-full hover:bg-indigo-700 p-2 rounded-lg transition-colors"
            >
              <img
                className="h-10 w-10 rounded-lg object-cover"
                src={user?.profileImage || `https://ui-avatars.com/api/?background=random&name=${user?.name}`}
                alt="User Profile"
              />
              {(!isMobile && isSidebarOpen) && (
                <div className="text-left">
                  <p className="text-sm font-medium text-white truncate">
                    {user?.name}
                  </p>
                  <p className="text-xs text-indigo-200">Sign out</p>
                </div>
              )}
            </button>
          </div>
        </div>
      </aside>

      {/* Main Content Area with Footer */}
      <div className="flex flex-col flex-1 overflow-hidden">
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>

        <Footer />
      </div>
    </div>
  );
};

export default Layout;