// File: ./components/SessionMonitor.tsx
import { useEffect } from 'react';
import { useAuthStore } from '../store/authStore';
import { doc, onSnapshot } from 'firebase/firestore';
import { db, auth } from '../firebase-config';
import { handleSessionLogout } from '../utils/session'; // Adjust path

function SessionMonitor() {
    const user = useAuthStore((state) => state.user);
    const sessionId = useAuthStore((state) => state.sessionId);

    useEffect(() => {
        if (!user || !sessionId) return;

        const userDocRef = doc(db, 'users', user.id);

        const unsubscribe = onSnapshot(userDocRef, (docSnap) => {
            const { activeSessionId } = docSnap.data() || {};
            if (activeSessionId !== sessionId) {
                handleSessionLogout(user.id);
            }
        });

        return () => unsubscribe();
    }, [user, sessionId]);

    return null;
}

export default SessionMonitor;