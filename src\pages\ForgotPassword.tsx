import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, Link } from 'react-router-dom';
import { GraduationCap } from 'lucide-react';
import {
    sendPasswordResetEmail,
    signInWithPopup,
    GoogleAuthProvider,
    linkWithCredential,
    EmailAuthProvider,
} from 'firebase/auth';
import { auth } from '../firebase-config';

const forgotPasswordSchema = z.object({
    email: z.string().email('Invalid email address'),
    newPassword: z
        .string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least one number')
        .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
        .optional(),
    confirmNewPassword: z.string().optional(),
}).refine((data) => !data.newPassword || data.newPassword === data.confirmNewPassword, {
    message: "Passwords don't match",
    path: ['confirmNewPassword'],
});

type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;

function ForgotPassword() {
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [showPasswordFields, setShowPasswordFields] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<ForgotPasswordForm>({
        resolver: zodResolver(forgotPasswordSchema),
    });

    const onSubmit = async (data: ForgotPasswordForm) => {
        setIsLoading(true);
        setMessage(null);
        setError(null);

        try {
            await sendPasswordResetEmail(auth, data.email, {
                url: `${window.location.origin}/login`,
                handleCodeInApp: true,
            });

            setMessage('If this email is registered, a password reset link has been sent.');
            setTimeout(() => navigate('/login'), 3000);
        } catch (error: any) {
            console.error('Forgot password error:', error);
            setError('An error occurred. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const onLinkPasswordSubmit = async (data: ForgotPasswordForm) => {
        if (!data.newPassword) {
            setError('Please enter a new password.');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const result = await signInWithPopup(auth, new GoogleAuthProvider());
            const currentUser = result.user;
            const credential = EmailAuthProvider.credential(data.email, data.newPassword);
            await linkWithCredential(currentUser, credential);

            setMessage('Password successfully linked! You can now log in with email and password.');
            setShowPasswordFields(false);
            setTimeout(() => navigate('/login'), 3000);
        } catch (error: any) {
            console.error('Linking error:', error);
            setError('Failed to link password. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div className="flex justify-center">
                    <GraduationCap className="h-12 w-12 text-primary-600" />
                </div>
                <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Reset Your Password</h2>
                <p className="mt-2 text-center text-sm text-gray-600">Enter your email to reset your password.</p>
            </div>
            <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    <form className="space-y-6" onSubmit={handleSubmit(showPasswordFields ? onLinkPasswordSubmit : onSubmit)}>
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email address</label>
                            <input
                                id="email"
                                type="email"
                                autoComplete="email"
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none"
                                {...register('email')}
                            />
                            {errors.email && <p className="text-sm text-red-600">{errors.email.message}</p>}
                        </div>

                        {showPasswordFields && (
                            <>
                                <div>
                                    <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">New Password</label>
                                    <input
                                        id="newPassword"
                                        type="password"
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none"
                                        {...register('newPassword')}
                                    />
                                    {errors.newPassword && <p className="text-sm text-red-600">{errors.newPassword.message}</p>}
                                </div>
                            </>
                        )}

                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none"
                        >
                            {isLoading ? 'Processing...' : showPasswordFields ? 'Link Password' : 'Reset Password'}
                        </button>
                    </form>

                    {message && <p className="mt-4 text-sm text-green-600">{message}</p>}
                    {error && <p className="mt-4 text-sm text-red-600">{error}</p>}

                    <p className="mt-6 text-center text-sm text-gray-600">
                        Back to <Link to="/login" className="text-primary-600 hover:text-primary-500">Sign in</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}

export default ForgotPassword;
