import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { auth, db } from '../firebase-config';
import { collection, getDocs, query, orderBy, where } from 'firebase/firestore';
import { Eye, Download, Calendar, Users, Database, Upload } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal';

interface SavedResult {
  id: string;
  examName: string;
  examId: string;
  totalQuestions: number;
  totalMarks: number;
  results: any[];
  statistics: any;
  createdBy: string;
  createdAt: string;
  isUploadMode: boolean;
  resultId: string;
}

const SavedResults: React.FC = () => {
  const navigate = useNavigate();
  const [savedResults, setSavedResults] = useState<SavedResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);

  // Load saved results from Firestore
  const loadSavedResults = async () => {
    if (!auth.currentUser) {
      setErrorMessage('Please log in to view saved results.');
      setIsErrorPopupOpen(true);
      return;
    }

    try {
      setLoading(true);
      const q = query(
        collection(db, 'examResults'),
        where('createdBy', '==', auth.currentUser.uid),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      const results = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SavedResult[];
      
      setSavedResults(results);
      console.log('Loaded saved results:', results);
    } catch (error) {
      console.error('Error loading saved results:', error);
      setErrorMessage('Failed to load saved results. Please try again.');
      setIsErrorPopupOpen(true);
    } finally {
      setLoading(false);
    }
  };

  // Export results as CSV
  const handleExportResults = (savedResult: SavedResult) => {
    const csvContent = [
      ['Seat No', 'Student Name', 'DOB', 'Address', 'Mobile No', 'Email ID', 'Aadhar', 'Gender', 'Age', 'Caste', 'Handicap', 'Score', 'Accuracy (%)', 'Correct', 'Incorrect', 'Unattempted', 'Time Spent (min)', 'Attempt Date'],
      ...savedResult.results.map((result: any) => [
        result.seatNo || '',
        result.studentName || '',
        result.dob || '',
        result.address || '',
        result.mobile || '',
        result.studentEmail || '',
        result.aadhar || '',
        result.gender || '',
        result.age || '',
        result.caste || '',
        result.handicap || '',
        result.score || 0,
        (result.accuracy || 0).toFixed(2),
        result.correct || 0,
        result.incorrect || 0,
        result.unattempted || 0,
        Math.round((result.totalTimeSpent || 0) / 60),
        result.attemptDate ? new Date(result.attemptDate).toLocaleString() : ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${savedResult.examName}_results.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // View results in detail
  const handleViewResults = (savedResult: SavedResult) => {
    // Navigate to exam results page with the saved data
    navigate(`/exam-results/${savedResult.examId}`, {
      state: { savedResultData: savedResult }
    });
  };

  useEffect(() => {
    loadSavedResults();
  }, []);

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-gray-600">Loading saved results...</div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Saved Results</h1>
        <p className="text-gray-600 mt-2">View and manage all your saved exam results</p>
      </div>

      {/* Results Grid */}
      {savedResults.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <Database className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">No Saved Results</h2>
          <p className="text-gray-600">
            You haven't saved any exam results yet. Save results from the exam results page to see them here.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {savedResults.map((savedResult) => (
            <div key={savedResult.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-800 text-lg mb-1">
                      {savedResult.examName}
                    </h3>
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      {savedResult.isUploadMode ? (
                        <Upload className="h-4 w-4 mr-1" />
                      ) : (
                        <Database className="h-4 w-4 mr-1" />
                      )}
                      {savedResult.isUploadMode ? 'From CSV Upload' : 'From Database'}
                    </div>
                  </div>
                </div>

                {/* Statistics */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    {savedResult.results?.length || 0} students
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    {new Date(savedResult.createdAt).toLocaleDateString()}
                  </div>
                  {savedResult.statistics && (
                    <div className="text-sm text-gray-600">
                      Avg Score: {savedResult.statistics.averageScore?.toFixed(1) || 0}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleViewResults(savedResult)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </button>
                  <button
                    onClick={() => handleExportResults(savedResult)}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => setIsErrorPopupOpen(false)}
      />
    </div>
  );
};

export default SavedResults;
