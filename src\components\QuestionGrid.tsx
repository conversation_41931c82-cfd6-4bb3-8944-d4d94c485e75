import React from 'react';

interface Question {
    status: "correct" | "incorrect" | "unattempted";
    questionIndex: number;
}

interface QuestionGridProps {
    questions: Question[];
    onQuestionClick: (index: number) => void;
}

const QuestionGrid: React.FC<QuestionGridProps> = ({ questions, onQuestionClick }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'correct':
                return 'bg-green-500 hover:bg-green-600';
            case 'incorrect':
                return 'bg-red-500 hover:bg-red-600';
            case 'unattempted':
                return 'bg-gray-500 hover:bg-gray-600';
            default:
                return 'bg-gray-500 hover:bg-gray-600';
        }
    };

    return (
        <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">Question Overview</h2>
            <div className="grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 gap-2">
                {questions.map((question, index) => (
                    <button
                        key={index}
                        onClick={() => onQuestionClick(index)}
                        className={`
                            ${getStatusColor(question.status)}
                            w-10 h-10 rounded-full
                            flex items-center justify-center
                            text-white font-medium
                            transition-colors duration-200
                            focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                        `}
                        title={`Question ${index + 1} - ${question.status.charAt(0).toUpperCase() + question.status.slice(1)}`}
                    >
                        {index + 1}
                    </button>
                ))}
            </div>
            <div className="mt-4 flex justify-center gap-6">
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-green-500"></div>
                    <span>Correct</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-red-500"></div>
                    <span>Incorrect</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-gray-500"></div>
                    <span>Not Attempted</span>
                </div>
            </div>
        </div>
    );
};

export default QuestionGrid;