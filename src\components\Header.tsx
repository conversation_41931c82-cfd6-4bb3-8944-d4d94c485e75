// src/components/Header.tsx
import React, { FC } from "react";

interface HeaderProps {
    correctAnswers: number;
    totalQuestions: number;
}

const Header: FC<HeaderProps> = ({ correctAnswers, totalQuestions }) => {
    return (
        <div className="bg-blue-500 text-white text-center py-6">
            <h1 className="text-2xl font-bold">{`${correctAnswers} out of ${totalQuestions} are correct`}</h1>
        </div>
    );
};

export default Header;
