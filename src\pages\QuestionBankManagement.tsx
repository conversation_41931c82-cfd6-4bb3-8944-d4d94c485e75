import React, { useState, useEffect, useCallback } from 'react';
import {
    Book,
    PlusCircle,
    Search,
    Edit2,
    Trash2,
    Eye,
    Filter,
    Flag,
    AlertTriangle
} from 'lucide-react';
import { QuestionModal } from '../components/QuestionModal';
import { QuestionForm } from '../components/QuestionForm';
import { auth } from '../firebase-config';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';

type Question = {
    questionId: string;
    questionNumber: number;
    question: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
    mark: number;
    negativeMark: number;
    duration: number;
    difficultyLevel: string;
    questionType: string;
    categoryId: string;
    category: string;
    topicId: string;
    topic: string;
    images: string[];
    tags: string[];
    createdAt: string;
    updatedAt: string;
};

type Category = {
    id: string;
    name: string;
    description: string | null;
    topics: Topic[];
};

type Topic = {
    id: string;
    name: string;
    categoryId: string;
    description: string | null;
};

type Report = {
    reportId: string;
    questionId: string;
    reportType: string;
    message: string;
    createdAt: string | null;
};

type QuestionWithReports = {
    question: Question;
    reports: Report[];
};

function QuestionBankManagement() {
    const [questions, setQuestions] = useState<Question[]>([]);
    const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isViewModalOpen, setIsViewModalOpen] = useState(false);
    const [selectedQuestion, setSelectedQuestion] = useState<Question | undefined>(undefined);
    const [searchQuery, setSearchQuery] = useState('');
    const [markRange, setMarkRange] = useState({ min: '', max: '' });
    const [sortConfig, setSortConfig] = useState({ key: 'questionNumber', direction: 'asc' });
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedTopic, setSelectedTopic] = useState('all');
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [totalElements, setTotalElements] = useState(0);
    const [pageSize] = useState(100);
    const [categories, setCategories] = useState<Category[]>([]);
    const [showReportedOnly, setShowReportedOnly] = useState(false);
    const [reportedQuestions, setReportedQuestions] = useState<Record<string, Report[]>>({});
    const [isReportModalOpen, setIsReportModalOpen] = useState(false);
    const [selectedReports, setSelectedReports] = useState<Report[]>([]);
    const [loading, setLoading] = useState(true);
    const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) throw new Error("Failed to get user ID token");

                    const [categoriesResponse, reportedResponse] = await Promise.all([
                        api.get("/api/categories/with-topics", {
                            headers: { Accept: '*/*', Authorization: `Bearer ${idToken}` },
                        }),
                        api.get("/api/reported-questions", {
                            headers: { Accept: '*/*', Authorization: `Bearer ${idToken}` },
                        }),
                    ]);

                    const categoriesData = categoriesResponse.data;
                    setCategories(categoriesData);

                    const reportedData: QuestionWithReports[] = reportedResponse.data;
                    const reportMap: Record<string, Report[]> = {};
                    reportedData.forEach((item) => {
                        reportMap[item.question.questionId] = item.reports;
                    });
                    setReportedQuestions(reportMap);
                } catch (error) {
                    console.error("Error fetching initial data:", error);
                    setErrorMessage(
                        error instanceof Error ? error.message : "Failed to load initial data"
                    );
                    setIsErrorPopupOpen(true);
                } finally {
                    setLoading(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.");
                setIsErrorPopupOpen(true);
                setLoading(false);
            }
        });

        return () => unsubscribe();
    }, []);

    // Fetch questions with dynamic pagination
    const fetchQuestions = useCallback(async (categoryId: string, topicId: string, reportedOnly: boolean, query: string = '', page: number = currentPage) => {
        if (categories.length === 0 || Object.keys(reportedQuestions).length === 0) return;

        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) throw new Error("Failed to get user ID token");

                    let url = `/api/questions?page=${page}&size=${pageSize}`;
                    if (categoryId !== 'all') url += `&categoryId=${categoryId}`;
                    if (topicId !== 'all' && topicId !== 'undefined') url += `&topicId=${topicId}`;

                    const response = await api.get(url, {
                        headers: { 'Accept': '*/*', 'Authorization': `Bearer ${idToken}` }
                    });
                    const data = response.data;;
                    const mappedQuestions = data.content.map((question: any) => {
                        const category = categories.find(cat => cat.id === question.categoryId);
                        const topic = category?.topics.find(t => t.id === question.topicId);
                        return {
                            ...question,
                            category: category?.name || 'Unknown',
                            topic: topic?.name || 'Unknown'
                        };
                    });

                    let filteredQuestions = mappedQuestions;
                    // Client-side min/max mark filtering
                    if (markRange.min || markRange.max) {
                        const min = markRange.min ? parseInt(markRange.min, 10) : -Infinity;
                        const max = markRange.max ? parseInt(markRange.max, 10) : Infinity;
                        filteredQuestions = mappedQuestions.filter((q: Question) =>
                            q.mark >= min && q.mark <= max
                        );
                    }
                    // Client-side search filtering
                    if (query) {
                        const queryLower = query.toLowerCase();
                        const queryNum = parseInt(query, 10);
                        filteredQuestions = filteredQuestions.filter((q: Question) =>
                            q.question.toLowerCase().includes(queryLower) ||
                            (isNaN(queryNum) ? false : q.questionNumber === queryNum)
                        );
                    }
                    if (reportedOnly) {
                        filteredQuestions = filteredQuestions.filter((q: Question) =>
                            reportedQuestions.hasOwnProperty(q.questionId)
                        );
                    }

                    setQuestions(filteredQuestions);
                    setTotalElements(data.page.totalElements);
                    setTotalPages(data.page.totalPages);
                } catch (error) {
                    console.error('Error fetching questions:', error);
                    setErrorMessage(error instanceof Error ? error.message : 'Failed to load questions');
                    setIsErrorPopupOpen(true);
                } finally {
                    setLoading(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.");
                setIsErrorPopupOpen(true);
                setLoading(false);
            }
            unsubscribe();
        });
    }, [categories, reportedQuestions, pageSize, markRange, currentPage]);

    // Initial fetch and re-fetch on changes
    useEffect(() => {
        fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
    }, [fetchQuestions, selectedCategory, selectedTopic, showReportedOnly, markRange, currentPage]);

    const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedCategory(e.target.value);
        setSelectedTopic('all');
        setCurrentPage(0);
        fetchQuestions(e.target.value, 'all', showReportedOnly, searchQuery);
    };

    const handleTopicChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedTopic(e.target.value);
        setCurrentPage(0);
        fetchQuestions(selectedCategory, e.target.value, showReportedOnly, searchQuery);
    };

    const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleSearchClick = () => {
        setCurrentPage(0); // Reset to first page on search
        fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
    };

    const handleMarkRangeChange = (type: 'min' | 'max', value: string) => {
        setMarkRange(prev => ({ ...prev, [type]: value }));
        setCurrentPage(0);
        fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 0 && newPage < totalPages) {
            setCurrentPage(newPage);
            fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery, newPage);
        }
    };

    const getTopicsForCategory = (categoryId: string) => {
        const category = categories.find(cat => cat.id === categoryId);
        return category ? category.topics : [];
    };

    const handleSort = (key: string) => {
        setSortConfig(current => ({
            key,
            direction: current.key === key && current.direction === 'asc' ? 'desc' : 'asc'
        }));
        const sortedQuestions = [...questions].sort((a, b) => {
            const aVal = key === 'questionNumber' ? a[key] : a[key]?.toString() || '';
            const bVal = key === 'questionNumber' ? b[key] : b[key]?.toString() || '';
            const direction = sortConfig.direction === 'asc' ? 1 : -1;
            return aVal > bVal ? direction : aVal < bVal ? -direction : 0;
        });
        setQuestions(sortedQuestions);
    };

    const handleAddQuestion = (formData: FormData) => {
        setIsAddModalOpen(false);
        fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
    };

    const handleEditQuestion = (formData: FormData) => {
        setIsEditModalOpen(false);
        fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
    };

    const handleDeleteQuestions = async () => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) throw new Error("Failed to get user ID token");

                    const questionIds = selectedQuestions.map(
                        (num) => questions.find((q) => q.questionNumber === num)?.questionId
                    );

                    await api.delete('/api/questions/bulk-delete', {
                        headers: {
                            'Content-Type': 'application/json',
                            Authorization: `Bearer ${idToken}`,
                        },
                        data: questionIds, // Axios uses `data` for the request body in DELETE requests
                    });

                    setIsDeleteModalOpen(false);
                    setSelectedQuestions([]);
                    fetchQuestions(selectedCategory, selectedTopic, showReportedOnly, searchQuery);
                } catch (error) {
                    console.error('Error deleting questions:', error);
                    setErrorMessage(error instanceof Error ? error.message : 'Failed to delete questions');
                    setIsErrorPopupOpen(true);
                } finally {
                    setLoading(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.");
                setIsErrorPopupOpen(true);
                setLoading(false);
            }
            unsubscribe();
        });
    };

    const handleReportedFilterToggle = () => {
        setShowReportedOnly(!showReportedOnly);
        setCurrentPage(0);
        fetchQuestions(selectedCategory, selectedTopic, !showReportedOnly, searchQuery);
    };

    const handleShowReports = (questionId: string) => {
        const reports = reportedQuestions[questionId] || [];
        setSelectedReports(reports);
        setIsReportModalOpen(true);
    };

    const startIndex = currentPage * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalElements);
    const currentQuestionsPage = questions;

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <div className="text-gray-600">Loading...</div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                    <Book className="h-6 w-6 text-gray-600" />
                    <h1 className="text-2xl font-semibold text-gray-900">Question Bank Management</h1>
                </div>
                <div className="flex space-x-3">
                    <button
                        onClick={() => setIsAddModalOpen(true)}
                        className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                    >
                        <PlusCircle className="h-5 w-5 mr-2" />
                        Add New Question
                    </button>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search by question or number..."
                                className="block w-full pr-10 py-2 border border-gray-300 rounded-md bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                                value={searchQuery}
                                onChange={handleSearchInputChange}
                            />
                            <button
                                onClick={handleSearchClick}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                                <Search className="h-5 w-5" />
                            </button>
                        </div>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Filter className="h-5 w-5 text-gray-400" />
                            </div>
                            <select
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                                value={selectedCategory}
                                onChange={handleCategoryChange}
                            >
                                <option value="all">All Categories</option>
                                {categories.map((category) => (
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Filter className="h-5 w-5 text-gray-400" />
                            </div>
                            <select
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                                value={selectedTopic}
                                onChange={handleTopicChange}
                                disabled={selectedCategory === 'all'}
                            >
                                <option value="all">All Topics</option>
                                {selectedCategory !== 'all' && getTopicsForCategory(selectedCategory).map((topic) => (
                                    <option key={topic.id} value={topic.id}>
                                        {topic.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="flex space-x-2">
                            <input
                                type="number"
                                placeholder="Min Mark"
                                className="block w-1/2 px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                                value={markRange.min}
                                onChange={(e) => handleMarkRangeChange('min', e.target.value)}
                            />
                            <input
                                type="number"
                                placeholder="Max Mark"
                                className="block w-1/2 px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                                value={markRange.max}
                                onChange={(e) => handleMarkRangeChange('max', e.target.value)}
                            />
                        </div>
                        <div className="flex items-center">
                            <button
                                onClick={handleReportedFilterToggle}
                                className={`flex items-center px-4 py-2 rounded-md border ${showReportedOnly
                                    ? 'bg-red-100 text-red-600 border-red-300'
                                    : 'border-gray-300 text-red-600'
                                    }`}
                            >
                                <Flag className={`h-5 w-5 mr-2 ${showReportedOnly ? 'text-red-600' : 'text-red-400'}`} />
                                Reported Questions
                            </button>
                        </div>
                    </div>
                </div>

                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div className="flex items-center justify-between">
                        <div className="flex-1 flex justify-between sm:hidden">
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 0}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                            >
                                Previous
                            </button>
                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={endIndex >= totalElements}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                            >
                                Next
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                                    <span className="font-medium">{endIndex}</span> of{' '}
                                    <span className="font-medium">{totalElements}</span> results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage === 0}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={endIndex >= totalElements}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    >
                                        Next
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {selectedQuestions.length > 0 && (
                <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">
                            {selectedQuestions.length} questions selected
                        </span>
                        <div className="flex space-x-3">
                            <button
                                onClick={handleDeleteQuestions}
                                className="px-3 py-1 text-sm text-red-600 border border-red-600 rounded-md hover:bg-red-50"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            )}

            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left">
                                <input
                                    type="checkbox"
                                    checked={selectedQuestions.length === currentQuestionsPage.length && currentQuestionsPage.length > 0}
                                    onChange={(e) => {
                                        setSelectedQuestions(
                                            e.target.checked ? currentQuestionsPage.map(q => q.questionNumber) : []
                                        );
                                    }}
                                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                />
                            </th>
                            <th
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('questionNumber')}
                            >
                                Question Number
                            </th>
                            <th
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('question')}
                            >
                                Question
                            </th>
                            <th
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('category')}
                            >
                                Category
                            </th>
                            <th
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('topic')}
                            >
                                Topic
                            </th>
                            <th
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('mark')}
                            >
                                Mark
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {currentQuestionsPage.map((question, index) => (
                            <tr key={question.questionNumber} className="hover:bg-gray-50">
                                <td className="px-6 py-4">
                                    <input
                                        type="checkbox"
                                        checked={selectedQuestions.includes(question.questionNumber)}
                                        onChange={(e) => {
                                            setSelectedQuestions(current =>
                                                e.target.checked
                                                    ? [...current, question.questionNumber]
                                                    : current.filter(num => num !== question.questionNumber)
                                            );
                                        }}
                                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                    />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {startIndex + index + 1}
                                </td>
                                <td className="px-6 py-4 text-sm text-gray-900">
                                    {question.question.length > 50
                                        ? `${question.question.substring(0, 50)}...`
                                        : question.question}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {question.category}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {question.topic}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {question.mark}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div className="flex justify-end space-x-3">
                                        {reportedQuestions[question.questionId] && (
                                            <button
                                                className="text-red-600 hover:text-red-900"
                                                onClick={() => handleShowReports(question.questionId)}
                                                title="View Reports"
                                            >
                                                <AlertTriangle className="h-5 w-5" />
                                            </button>
                                        )}
                                        <button
                                            className="text-primary-600 hover:text-primary-900"
                                            onClick={() => {
                                                setSelectedQuestion(question);
                                                setIsViewModalOpen(true);
                                            }}
                                        >
                                            <Eye className="h-5 w-5" />
                                        </button>
                                        <button
                                            className="text-primary-600 hover:text-primary-900"
                                            onClick={() => {
                                                setSelectedQuestion(question);
                                                setIsEditModalOpen(true);
                                            }}
                                        >
                                            <Edit2 className="h-5 w-5" />
                                        </button>
                                        <button
                                            className="text-red-600 hover:text-red-900"
                                            onClick={() => {
                                                setSelectedQuestion(question);
                                                setIsDeleteModalOpen(true);
                                            }}
                                        >
                                            <Trash2 className="h-5 w-5" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <QuestionModal
                isOpen={isAddModalOpen}
                onClose={() => setIsAddModalOpen(false)}
                title="Add New Question"
            >
                <QuestionForm onSubmit={handleAddQuestion} categories={categories} />
            </QuestionModal>

            <QuestionModal
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                title="Edit Question"
            >
                <QuestionForm onSubmit={handleEditQuestion} initialData={selectedQuestion} isEdit categories={categories} />
            </QuestionModal>

            <QuestionModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                title="Delete Question"
            >
                <div className="space-y-4">
                    <p className="text-gray-500">
                        Are you sure you want to delete{' '}
                        {selectedQuestions.length > 1
                            ? `${selectedQuestions.length} questions`
                            : 'this question'}? This action cannot be undone.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => setIsDeleteModalOpen(false)}
                            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleDeleteQuestions}
                            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                        >
                            Delete
                        </button>
                    </div>
                </div>
            </QuestionModal>

            <QuestionModal
                isOpen={isViewModalOpen}
                onClose={() => setIsViewModalOpen(false)}
                title="View Question"
            >
                {selectedQuestion && (
                    <div className="space-y-4">
                        <div>
                            <h3 className="font-medium">Question:</h3>
                            <p>{selectedQuestion.question}</p>
                        </div>
                        <div>
                            <h3 className="font-medium">Options:</h3>
                            <ul className="list-disc list-inside">
                                {selectedQuestion.options.map((option, index) => (
                                    <li key={index}>{option}</li>
                                ))}
                            </ul>
                        </div>
                        <div>
                            <h3 className="font-medium">Correct Answer:</h3>
                            <p>{selectedQuestion.correctAnswer}</p>
                        </div>
                        <div>
                            <h3 className="font-medium">Explanation:</h3>
                            <p>{selectedQuestion.explanation}</p>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <h3 className="font-medium">Mark:</h3>
                                <p>{selectedQuestion.mark}</p>
                            </div>
                            <div>
                                <h3 className="font-medium">Negative Mark:</h3>
                                <p>{selectedQuestion.negativeMark}</p>
                            </div>
                            <div>
                                <h3 className="font-medium">Duration (seconds):</h3>
                                <p>{selectedQuestion.duration}</p>
                            </div>
                            <div>
                                <h3 className="font-medium">Category:</h3>
                                <p>{selectedQuestion.category}</p>
                            </div>
                            <div>
                                <h3 className="font-medium">Topic:</h3>
                                <p>{selectedQuestion.topic}</p>
                            </div>
                        </div>
                    </div>
                )}
            </QuestionModal>

            <QuestionModal
                isOpen={isReportModalOpen}
                onClose={() => setIsReportModalOpen(false)}
                title="Question Reports"
            >
                <div className="space-y-4">
                    {selectedReports.map((report, index) => (
                        <div key={report.reportId} className="p-4 bg-red-50 rounded-lg">
                            <div className="flex items-start justify-between">
                                <div>
                                    <h4 className="font-medium text-red-700">Report Type: {report.reportType}</h4>
                                    {report.message && (
                                        <p className="mt-1 text-red-600">{report.message}</p>
                                    )}
                                </div>
                                {report.createdAt && (
                                    <span className="text-sm text-red-500">
                                        {new Date(report.createdAt).toLocaleDateString()}
                                    </span>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </QuestionModal>

            <ErrorPopupModal
                isOpen={isErrorPopupOpen}
                message={errorMessage || 'An error occurred'}
                onClose={() => setIsErrorPopupOpen(false)}
                onOk={() => {
                    setIsErrorPopupOpen(false);
                    window.location.reload();
                }}
            />
        </div>
    );
}

export default QuestionBankManagement;