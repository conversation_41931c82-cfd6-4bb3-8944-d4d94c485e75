// src/components/QuestionNavigatorModal.tsx
import React, { FC } from "react";

interface QuestionNavigatorModalProps {
    questions: { status: "answered" | "notAttempted" | "reviewLater" }[];
    currentIndex: number;
    onJumpToQuestion: (index: number) => void;
    onFinishExam: () => void;
}

const QuestionNavigatorModal: FC<QuestionNavigatorModalProps> = ({
    questions,
    currentIndex,
    onJumpToQuestion,
    onFinishExam,
}) => {
    return (
        <div className="w-full p-4 bg-white">
            {/* Legends */}
            <div className="flex justify-start gap-4 mb-4">
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-gray-300"></div>
                    <span className="text-sm">Not Attempted</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-green-300"></div>
                    <span className="text-sm">Answered</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-yellow-300"></div>
                    <span className="text-sm">Review Later</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                    <span className="text-sm">Current</span>
                </div>
            </div>

            {/* Question Navigation */}
            <div className="max-h-40 overflow-y-auto">
                <div className="grid grid-cols-10 gap-2 mb-4">
                    {questions.map((q, index) => (
                        <button
                            key={index}
                            onClick={() => onJumpToQuestion(index)}
                            className={`w-8 h-8 rounded-full text-sm ${index === currentIndex
                                    ? "bg-blue-500 text-white"
                                    : q.status === "answered"
                                        ? "bg-green-300"
                                        : q.status === "reviewLater"
                                            ? "bg-yellow-300"
                                            : "bg-gray-300"
                                }`}
                        >
                            {index + 1}
                        </button>
                    ))}
                </div>
            </div>

            <div className="flex justify-end">
                <button
                    onClick={onFinishExam}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                    Finish Exam
                </button>
            </div>
        </div>
    );
};

export default QuestionNavigatorModal;