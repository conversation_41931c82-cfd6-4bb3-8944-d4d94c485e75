// src/pages/Unauthorized.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertCircle, ArrowLeft, Home } from 'lucide-react'; // Lucide React icons

const Unauthorized = () => {
    const navigate = useNavigate();

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center px-4">
            <div className="max-w-md w-full text-center space-y-8 animate-fade-in-up">
                {/* Icon */}
                <div className="inline-flex items-center justify-center bg-gradient-to-br from-purple-500 to-blue-500 rounded-full p-6 shadow-lg">
                    <AlertCircle className="w-12 h-12 text-white" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                    <h1 className="text-4xl font-bold text-gray-900 tracking-tight">
                        Access Restricted
                    </h1>
                    <p className="text-lg text-gray-600 leading-relaxed">
                        You don't have permission to view this page. Please contact your
                        administrator or return to your dashboard.
                    </p>
                </div>

                {/* Image */}
                <div className="rounded-lg overflow-hidden shadow-lg">
                    <img
                        src="https://source.unsplash.com/800x600/?security,access"
                        alt="Security"
                        className="w-full h-48 object-cover"
                    />
                </div>

                {/* Buttons */}
                <div className="flex justify-center gap-4">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                    >
                        <ArrowLeft className="w-5 h-5" />
                        Go Back
                    </button>
                    <button
                        onClick={() => navigate('/dashboard')}
                        className="flex items-center gap-2 px-6 py-3 bg-gradient-to-br from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200"
                    >
                        <Home className="w-5 h-5" />
                        Return to Dashboard
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Unauthorized;