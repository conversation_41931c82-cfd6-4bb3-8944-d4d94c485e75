import React, { useState, useEffect } from 'react';
import { useLocation, Navigate, useNavigate } from 'react-router-dom';
import { MailCheck, ArrowLeft } from 'lucide-react';

function VerifyEmail() {
  const location = useLocation();
  const navigate = useNavigate();
  const email = location.state?.email;
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (!canResend && timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else if (timer === 0) {
      setCanResend(true);
    }
  }, [timer, canResend]);

  if (!email) {
    return <Navigate to="/register" replace />;
  }

  const handleResendEmail = async () => {
    try {
      // Here you would typically call your resend verification email function
      setTimer(60);
      setCanResend(false);
    } catch (error) {
      console.error('Failed to resend verification email:', error);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-[#ECF4F4] flex flex-col py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <button
          onClick={() => navigate(-1)}
          className="mb-8 ml-4 inline-flex items-center justify-center w-11 h-11 rounded-full bg-[#228cdc] text-white hover:bg-[#1a7ac0] transition-colors"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>

        <div className="text-center">
          <MailCheck className="mx-auto h-12 w-12 text-[#228cdc]" />
          <h2 className="mt-6 text-2xl font-normal text-gray-900">
            Check your inbox to verify
          </h2>
          <h2 className="text-2xl font-normal text-gray-900">
            your email,
          </h2>
          <p className="mt-2 text-sm text-gray-600 font-light">
            Sign up again if link has expired.
          </p>

          <div className="mt-4">
            <p className="text-sm text-gray-600">
              We sent a verification link to
            </p>
            <p className="mt-1 text-sm font-medium text-gray-900">{email}</p>
          </div>

          {canResend ? (
            <button
              onClick={handleResendEmail}
              className="mt-4 text-sm text-[#228cdc] hover:text-[#1a7ac0] font-medium"
            >
              Resend verification email
            </button>
          ) : (
            <p className="mt-4 text-sm text-gray-600">
              Resend email in {timer} seconds
            </p>
          )}
        </div>

        <div className="mt-8">
          <button
            onClick={handleBackToLogin}
            className="w-full h-14 flex justify-center items-center px-4 rounded-3xl shadow-sm text-sm font-medium text-white bg-[#228cdc] hover:bg-[#1a7ac0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#228cdc] transition-colors"
          >
            Go to Log In
          </button>
        </div>
      </div>
    </div>
  );
}

export default VerifyEmail;