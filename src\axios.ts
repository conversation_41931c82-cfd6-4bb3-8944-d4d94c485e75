import axios from 'axios';
import { useAuthStore } from './store/authStore';
// Logger utility
const log = (message, ...args) => console.log(`[Axios Interceptor] ${message}`, ...args);

log('axios.ts loaded');

const api = axios.create({
    baseURL: 'https://examate-backend.el.r.appspot.com/',
});

log('API instance created');

const ensureStoreReady = () => {
    log('Entering ensureStoreReady');
    const state = useAuthStore.getState();
    log('Initial state:', state);

    if (!state.sessionId && typeof window !== 'undefined') {
        log('sessionId is null, waiting for rehydration');
        return new Promise((resolve) => {
            setTimeout(() => {
                const rehydratedState = useAuthStore.getState();
                log('Rehydrated state after delay:', rehydratedState);
                resolve(rehydratedState);
            }, 100);
        });
    }
    log('sessionId present or no rehydration needed, resolving immediately');
    return Promise.resolve(state);
};

// Request interceptor
api.interceptors.request.use(
    async (config) => {
        log('Request interceptor triggered for URL:', config.url);
        const { sessionId, token } = await ensureStoreReady();
        log('Retrieved sessionId:', sessionId, 'token:', token);

        if (sessionId) {
            config.headers['X-Session-ID'] = sessionId;
            log('Set X-Session-ID header:', config.headers['X-Session-ID']);
        } else {
            log('No sessionId available, skipping X-Session-ID header');
        }

        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
            log('Set Authorization header:', config.headers['Authorization']);
        } else {
            log('No token available, skipping Authorization header');
        }

        log('Final request config headers:', config.headers);
        return config;
    },
    (error) => {
        log('Request interceptor error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor with retry limit
const MAX_RETRIES = 1; // Limit retries to 1

api.interceptors.response.use(
    (response) => {
        log('Response received for URL:', response.config.url, 'Status:', response.status, "sessionID:", response.headers['x-session-id']);
        return response;
    },
    async (error) => {
        log('Response error for URL:', error.config?.url, 'Status:', error.response?.status);

        if (error.response?.status === 401) {
            const { logout, refreshToken } = useAuthStore.getState();
            const errorData = error.response?.data;

            // Check for invalid session
            if (errorData === "Invalid or expired session") {
                log('Invalid session detected due to one-user-one-session policy');
                alert('Your account is logged in elsewhere. This session has been terminated due to our one-user-one-session policy.');
                logout((path) => (window.location.href = path));
                return Promise.reject(error);
            }

            // Check for expired Firebase token
            if (errorData?.error === "Firebase token expired, please refresh") {
                log('Firebase token expired detected');
                // Retry logic for token expiration
                let retries = error.config._retryCount || 0;
                if (retries < MAX_RETRIES) {
                    error.config._retryCount = retries + 1;
                    log(`Retry attempt ${retries + 1} of ${MAX_RETRIES}`);

                    try {
                        await refreshToken();
                        const newToken = useAuthStore.getState().token;
                        const retrySessionId = useAuthStore.getState().sessionId;
                        log('Token refreshed. New token:', newToken, 'Retry sessionId:', retrySessionId);

                        error.config.headers['Authorization'] = `Bearer ${newToken}`;
                        if (retrySessionId) {
                            error.config.headers['X-Session-ID'] = retrySessionId;
                            log('Set retry X-Session-ID header:', error.config.headers['X-Session-ID']);
                        } else {
                            log('No retry sessionId available');
                        }
                        log('Retrying request with updated headers:', error.config.headers);
                        return api.request(error.config);
                    } catch (refreshError) {
                        log('Token refresh failed:', refreshError);
                        alert('Session expired. Please log in again.');
                        logout((path) => (window.location.href = path));
                        return Promise.reject(refreshError);
                    }
                } else {
                    log('Max retries reached, logging out');
                    alert('Session expired after multiple attempts. Please log in again.');
                    logout((path) => (window.location.href = path));
                    return Promise.reject(error);
                }
            }

            // Generic 401 handling (e.g., invalid token format)
            log('Generic 401 error, logging out');
            alert('Invalid session detected due to one-user-one-session policy. Your account might be logged in elsewhere.  Authentication failed. Please log in again.');
            logout((path) => (window.location.href = path));
            return Promise.reject(error);
        }
        return Promise.reject(error);
    }
);

export default api;