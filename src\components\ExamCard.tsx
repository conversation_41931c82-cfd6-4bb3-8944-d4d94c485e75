import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, Calendar, Award } from 'lucide-react';

interface Props {
  exam: {
    examId: string;
    examName: string;
    examDate: string;
    passingMarks: number;
    totalMarks: number | null;
    totalQuestions: number | null;
    duration: number | null;
    status: string;
    instructions: string;
    description: string;
    examType: string;
    examCategoryId: string;
  };
}

const ExamCard: React.FC<Props> = ({ exam }) => {
  const navigate = useNavigate();

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {exam.examType}
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {exam.status}
          </span>
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900">{exam.examName}</h3>
        <div className="mt-4 space-y-3">
          {exam.examDate && (
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="flex-shrink-0 mr-1.5 h-4 w-4" />
              {new Date(exam.examDate).toLocaleDateString()}
            </div>
          )}
          {exam.duration && (
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="flex-shrink-0 mr-1.5 h-4 w-4" />
              {exam.duration} minutes
            </div>
          )}
          {exam.totalMarks && (
            <div className="flex items-center text-sm text-gray-500">
              <Award className="flex-shrink-0 mr-1.5 h-4 w-4" />
              {exam.totalMarks} marks
            </div>
          )}
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
        </div>
        <div className="mt-6 flex space-x-2">
          <button
            className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            onClick={() => navigate(`/exams_management/view-questions/${exam.examId}`)}
          >
            View Questions
          </button>
          <button
            className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={() => navigate(`/exams_management/add-questions/${exam.examId}`)}
          >
            Add Questions
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExamCard;