// src/utils/apiErrorHandler.ts
import { NavigateFunction } from 'react-router-dom';

export const handleApiError = (
    response: Response,
    navigate: NavigateFunction,
    setErrorMessage: React.Dispatch<React.SetStateAction<string | null>>,
    setIsErrorPopupOpen: React.Dispatch<React.SetStateAction<boolean>>
) => {
    if (response.status === 401 || response.status === 403) {
        navigate('/unauthorized');
        return true; // Indicate error is handled
    }
    return false;
};

