import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Clock } from "lucide-react";

function ExamPage() {
    const { examId } = useParams();
    const navigate = useNavigate();

    // Mock exam data - replace with API call
    const exam = {
        id: examId,
        title: "IBPS PO Preliminary Exam 2024",
        duration: 60, // in minutes
        totalMarks: 100,
        passingMarks: 40,
        questions: [
            {
                id: 1,
                text: "What is the capital of France?",
                options: ["Paris", "London", "Berlin", "Madrid"],
                correctAnswer: "Paris",
                marks: 2,
            },
            {
                id: 2,
                text: "Who wrote 'Pride and Prejudice'?",
                options: [
                    "<PERSON> Austen",
                    "<PERSON> Twain",
                    "<PERSON> Dickens",
                    "J.K<PERSON> Rowling",
                ],
                correctAnswer: "Jane Austen",
                marks: 2,
            },
            {
                id: 3,
                text: "Which planet is known as the Red Planet?",
                options: ["Earth", "Mars", "Venus", "Jupiter"],
                correctAnswer: "Mars",
                marks: 2,
            },
        ],
    };

    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [timeLeft, setTimeLeft] = useState(exam.duration * 60); // Duration in seconds
    const [answers, setAnswers] = useState({});
    const [isExamCompleted, setIsExamCompleted] = useState(false);
    const [score, setScore] = useState(0);

    const currentQuestion = exam.questions[currentQuestionIndex];

    useEffect(() => {
        if (timeLeft > 0) {
            const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
            return () => clearInterval(timer);
        } else {
            handleSubmitExam(); // Auto-submit when time runs out
        }
    }, [timeLeft]);

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    const handleAnswerChange = (questionId, option) => {
        setAnswers((prev) => ({ ...prev, [questionId]: option }));
    };

    const handleNext = () => {
        if (currentQuestionIndex < exam.questions.length - 1) {
            setCurrentQuestionIndex((prev) => prev + 1);
        }
    };

    const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex((prev) => prev - 1);
        }
    };

    const handleSubmitExam = () => {
        let totalScore = 0;

        exam.questions.forEach((question) => {
            if (answers[question.id] === question.correctAnswer) {
                totalScore += question.marks;
            }
        });

        setScore(totalScore);
        setIsExamCompleted(true);
    };

    if (isExamCompleted) {
        return (
            <div className="max-w-4xl mx-auto text-center mt-10">
                <h1 className="text-2xl font-bold text-gray-900">Exam Completed</h1>
                <p className="text-lg text-gray-700 mt-4">
                    You scored <span className="text-green-600 font-bold">{score}</span> out of{" "}
                    {exam.totalMarks}.
                </p>
                <p className="text-gray-600 mt-2">
                    {score >= exam.passingMarks ? (
                        <span className="text-green-600">Congratulations! You passed the exam.</span>
                    ) : (
                        <span className="text-red-600">Unfortunately, you did not pass the exam.</span>
                    )}
                </p>
                <button
                    className="px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                    onClick={() => navigate("/dashboard")}
                >
                    Return to Dashboard
                </button>
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto">
            <div className="bg-white shadow rounded-lg">
                {/* Header */}
                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h1 className="text-2xl font-semibold text-gray-900">{exam.title}</h1>
                    <div className="text-red-600 font-bold">
                        Time Left: {formatTime(timeLeft)}
                    </div>
                </div>

                {/* Question */}
                <div className="px-6 py-4">
                    <h2 className="text-lg font-medium text-gray-900">
                        Question {currentQuestionIndex + 1}/{exam.questions.length}
                    </h2>
                    <p className="mt-2 text-gray-700">{currentQuestion.text}</p>
                    <div className="mt-4 space-y-2">
                        {currentQuestion.options.map((option, index) => (
                            <label
                                key={index}
                                className="flex items-center space-x-2 cursor-pointer"
                            >
                                <input
                                    type="radio"
                                    name={`question-${currentQuestion.id}`}
                                    value={option}
                                    checked={answers[currentQuestion.id] === option}
                                    onChange={() => handleAnswerChange(currentQuestion.id, option)}
                                    className="h-4 w-4"
                                />
                                <span className="text-gray-700">{option}</span>
                            </label>
                        ))}
                    </div>
                </div>

                {/* Navigation Buttons */}
                <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
                    <button
                        onClick={handlePrevious}
                        disabled={currentQuestionIndex === 0}
                        className={`px-4 py-2 border rounded-md text-sm font-medium ${currentQuestionIndex === 0
                            ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                            : "text-gray-700 hover:bg-gray-50"
                            }`}
                    >
                        Previous
                    </button>
                    {currentQuestionIndex < exam.questions.length - 1 ? (
                        <button
                            onClick={handleNext}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                        >
                            Next
                        </button>
                    ) : (
                        <button
                            onClick={handleSubmitExam}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                        >
                            Submit
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}

export default ExamPage;
