import React, { useState } from "react";

interface PublishTestProps {
    testId: string;
    title: string;
}

const PublishTest: React.FC<PublishTestProps> = ({ testId, title }) => {
    const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
    const [notificationType, setNotificationType] = useState<string>("email");
    const [isPublished, setIsPublished] = useState(false);

    const handleGroupSelection = (group: string) => {
        setSelectedGroups((prevGroups) =>
            prevGroups.includes(group)
                ? prevGroups.filter((g) => g !== group)
                : [...prevGroups, group]
        );
    };

    const handlePublish = () => {
        if (selectedGroups.length === 0) {
            alert("Please select at least one group to publish the test.");
            return;
        }
        setIsPublished(true);
        alert(`Test "${title}" has been published!`);
    };

    return (
        <div className="p-6 bg-white rounded shadow">
            <h2 className="text-xl font-bold mb-4">Publish Test</h2>
            <p className="mb-2">Test Title: {title}</p>

            <div className="mb-4">
                <h3 className="font-medium mb-2">Select Groups:</h3>
                {["Group A", "Group B", "Group C"].map((group) => (
                    <label key={group} className="flex items-center mb-2">
                        <input
                            type="checkbox"
                            value={group}
                            checked={selectedGroups.includes(group)}
                            onChange={() => handleGroupSelection(group)}
                            className="mr-2"
                        />
                        {group}
                    </label>
                ))}
            </div>

            <div className="mb-4">
                <h3 className="font-medium mb-2">Notification Type:</h3>
                <label className="flex items-center mb-2">
                    <input
                        type="radio"
                        name="notificationType"
                        value="email"
                        checked={notificationType === "email"}
                        onChange={(e) => setNotificationType(e.target.value)}
                        className="mr-2"
                    />
                    Email
                </label>
                <label className="flex items-center mb-2">
                    <input
                        type="radio"
                        name="notificationType"
                        value="sms"
                        checked={notificationType === "sms"}
                        onChange={(e) => setNotificationType(e.target.value)}
                        className="mr-2"
                    />
                    SMS
                </label>
            </div>

            <button
                onClick={handlePublish}
                disabled={isPublished}
                className={`${isPublished ? "bg-gray-400" : "bg-blue-500"
                    } text-white px-6 py-2 rounded`}
            >
                {isPublished ? "Test Published" : "Publish Test"}
            </button>
        </div>
    );
};

export default PublishTest;
