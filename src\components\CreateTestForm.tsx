import React, { useRef, useState } from "react";
import { collection, addDoc } from "firebase/firestore";
import { db } from "../firebase-config"; // Import the Firestore instance from firebase-config
import { ChevronDown, ChevronRight } from "lucide-react"; // Import icons
import Papa from "papaparse";

interface QuestionPool {
    id: string;
    question: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
    mark: number;
    negativeMark: number;
    duration: number;
    category: string;
    topic: string;
}

const CreateTestForm: React.FC = () => {
    const [testType, setTestType] = useState("MCQs");
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [duration, setDuration] = useState<number>(60);
    const [passingMarks, setPassingMarks] = useState<number>(0);
    const [maxAttempts, setMaxAttempts] = useState<number>(1);
    const [questionPool, setQuestionPool] = useState<QuestionPool[]>([]);
    const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [isCollapsed, setIsCollapsed] = useState<boolean>(true); // State for toggling visibility

    const handleAddQuestion = () => {
        const newQuestion: QuestionPool = {
            id: `${Date.now()}`,
            question: "",
            options: ["", "", "", ""],
            correctAnswer: "",
            explanation: "",
            mark: 1,
            negativeMark: -1,
            duration: 60,
            category: "",
            topic: "",
        };
        setQuestionPool([...questionPool, newQuestion]);
    };

    const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            const fileContent = event.target?.result as string;
            const importedQuestions = parseCSV(fileContent); // Parse CSV and convert to QuestionPool format.
            setQuestionPool([...questionPool, ...importedQuestions]);
            alert("Questions imported successfully!");
        };
        reader.readAsText(file);
    };

    const parseCSV = (data: string): QuestionPool[] => {
        const result = Papa.parse(data, {
            header: true,
            skipEmptyLines: true,
        });

        return result.data.map((row: any, index: number) => ({
            id: `${Date.now()}-${index}`,
            question: row.question?.trim() || "",
            options: row.options?.split(";").map((opt: string) => opt.trim()) || [],
            correctAnswer: row.correctAnswer?.trim() || "",
            explanation: row.explanation?.trim() || "",
            mark: parseFloat(row.mark?.trim() || "1"),
            negativeMark: parseFloat(row.negativeMark?.trim() || "-1"),
            duration: parseInt(row.duration?.trim() || "60", 10),
            category: row.category?.trim() || "",
            topic: row.topic?.trim() || "",
        }));
    };

    const handlePreview = () => {
        if (questionPool.length === 0) {
            alert("No questions available for preview!");
            return;
        }
        setIsPreviewOpen(true);
    };

    const handleExport = (format: "csv" | "json") => {
        const sampleData = [
            {
                questionNumber: 1,
                question: "What is the value of 25 + 36?",
                options: ["51", "61", "71", "81"],
                correctAnswer: "61",
                explanation: "25 + 36 = 61",
                mark: 1,
                negativeMark: -1,
                duration: 60,
                category: "general aptitude",
                topic: "addition",
            },
        ];

        let content = "";
        if (format === "csv") {
            content =
                "questionNumber,question,options,correctAnswer,explanation,mark,negativeMark,duration,category,topic\n";
            sampleData.forEach((q) => {
                content += `${q.questionNumber},"${q.question}","${q.options.join(";")}","${q.correctAnswer}","${q.explanation}",${q.mark},${q.negativeMark},${q.duration},"${q.category}","${q.topic}"\n`;
            });
        } else if (format === "json") {
            content = JSON.stringify(sampleData, null, 2);
        }

        const blob = new Blob([content], { type: format === "csv" ? "text/csv" : "application/json" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `questions.${format}`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const handleSubmit = async () => {
        if (questionPool.length === 0) {
            alert("Please add questions before creating the test.");
            return;
        }

        const testDetails = {
            testType,
            title,
            description,
            duration,
            passingMarks,
            maxAttempts,
            questionPool,
        };

        try {
            // Add the test data to Firestore
            const docRef = await addDoc(collection(db, "tests"), testDetails);

            // Optionally, reset the form after successful submission
            setTitle("");
            setDescription("");
            setDuration(60);
            setPassingMarks(0);
            setMaxAttempts(1);
            setQuestionPool([]);
        } catch (error) {
            console.error("Error creating test: ", error);
            alert("Error creating test. Please try again.");
        }
    };

    const toggleCollapse = () => {
        setIsCollapsed((prev) => !prev);
    };

    return (
        <div className="p-6 bg-white rounded shadow">
            <h2
                onClick={toggleCollapse}
                className="text-xl font-bold mb-4 flex justify-between items-center bg-gray-100 px-4 py-2"
            >
                Create Test
                <button
                    onClick={toggleCollapse}
                    className="flex items-center text-black"
                >
                    {isCollapsed ? (
                        <ChevronRight className="w-5 h-5" />
                    ) : (
                        <ChevronDown className="w-5 h-5" />
                    )}
                </button>
            </h2>
            {/* Collapsible section */}
            {!isCollapsed && (
                <form>
                    <div className="mb-4">
                        <label className="block font-medium">Test Type</label>
                        <select
                            value={testType}
                            onChange={(e) => setTestType(e.target.value)}
                            className="border rounded p-2 w-full"
                        >
                            <option value="MCQs">MCQs</option>
                            <option value="Subjective">Subjective</option>
                            <option value="Coding">Coding</option>
                        </select>
                    </div>

                    <div className="mb-4">
                        <label className="block font-medium">Title</label>
                        <input
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            className="border rounded p-2 w-full"
                        />
                    </div>

                    <div className="mb-4">
                        <label className="block font-medium">Description</label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="border rounded p-2 w-full"
                        />
                    </div>

                    <div className="flex gap-4 mb-4">
                        <div className="flex-1">
                            <label className="block font-medium">Duration (minutes)</label>
                            <input
                                type="number"
                                value={duration}
                                onChange={(e) => setDuration(Number(e.target.value))}
                                className="border rounded p-2 w-full"
                            />
                        </div>
                        <div className="flex-1">
                            <label className="block font-medium">Passing Marks</label>
                            <input
                                type="number"
                                value={passingMarks}
                                onChange={(e) => setPassingMarks(Number(e.target.value))}
                                className="border rounded p-2 w-full"
                            />
                        </div>
                        <div className="flex-1">
                            <label className="block font-medium">Max Attempts</label>
                            <input
                                type="number"
                                value={maxAttempts}
                                onChange={(e) => setMaxAttempts(Number(e.target.value))}
                                className="border rounded p-2 w-full"
                            />
                        </div>
                    </div>

                    {/* Import/Export Section */}
                    <div className="mb-6">
                        <h3 className="text-lg font-bold mb-2">Import/Export Questions</h3>

                        <div className="mb-4">
                            <h4 className="font-medium">Import Questions:</h4>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept=".csv,.json"
                                onChange={handleImport}
                                className="border p-2 rounded w-full"
                            />
                            <p className="text-sm text-gray-500 mt-1">Accepted formats: CSV, JSON</p>
                        </div>
                        <div className="mb-4">
                            <h4 className="font-medium">Export Sample Format of Questions:</h4>
                            <button
                                type="button"
                                onClick={() => handleExport("csv")}
                                className="bg-blue-500 text-white px-4 py-1 rounded mr-2"
                            >
                                Export as CSV
                            </button>
                            <button
                                type="button"
                                onClick={() => handleExport("json")}
                                className="bg-blue-500 text-white px-4 py-1 rounded"
                            >
                                Export as JSON
                            </button>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="button"
                            onClick={handlePreview}
                            className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
                        >
                            Preview Questions
                        </button>
                        <button
                            type="button"
                            onClick={handleSubmit}
                            className="bg-green-500 text-white px-6 py-2 rounded"
                        >
                            Create Test
                        </button>
                    </div>
                </form>
            )}
            {/* Preview Modal */}
            {isPreviewOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
                    <div className="bg-white rounded-lg shadow-lg p-6 w-3/4 h-[80vh] flex flex-col">
                        {/* Fixed Header */}
                        <div className="sticky top-0 bg-white z-10 p-4 border-b">
                            <div className="flex justify-between items-center">
                                <h2 className="text-xl font-bold">Question Preview</h2>
                                <button
                                    onClick={() => setIsPreviewOpen(false)}
                                    className="bg-red-500 text-white px-4 py-2 rounded"
                                >
                                    Close Preview
                                </button>
                            </div>
                        </div>

                        {/* Scrollable Question List */}
                        <div className="overflow-y-auto flex-grow p-4">
                            {questionPool.map((question, index) => (
                                <div key={question.id} className="border p-4 rounded mb-4">
                                    <h3 className="font-bold mb-2">Q{index + 1}: {question.question}</h3>
                                    <ul className="mb-2">
                                        {question.options.map((option, i) => (
                                            <li key={i} className="ml-4 list-disc">{option}</li>
                                        ))}
                                    </ul>
                                    <p><strong>Correct Answer:</strong> {question.correctAnswer}</p>
                                    <p><strong>Explanation:</strong> {question.explanation}</p>
                                    <p><strong>Category:</strong> {question.category}</p>
                                    <p><strong>Topic:</strong> {question.topic}</p>
                                    <p><strong>Marks:</strong> {question.mark}</p>
                                    <p><strong>Negative Mark:</strong> {question.negativeMark}</p>
                                    <p><strong>Duration:</strong> {question.duration} seconds</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CreateTestForm;