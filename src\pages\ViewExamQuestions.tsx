import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { auth } from '../firebase-config';
import { Trash2 } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';

const ViewExamQuestions: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const [examData, setExamData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isDeleteExamPopupOpen, setIsDeleteExamPopupOpen] = useState(false);
  const navigate = useNavigate();

  // Fetch exam questions
  useEffect(() => {
    if (!examId) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const response = await api.get(`/api/exams/${examId}/with-questions`, {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`,
            },
          });
          const data = response.data;;
          setExamData(data);
        } catch (err) {
          setErrorMessage(err instanceof Error ? err.message : 'Failed to fetch exam questions');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [examId]);

  const handleDeleteQuestion = async (questionId: string) => {
    if (!window.confirm('Are you sure you want to delete this question?')) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error('Failed to get user ID token');
          }

          // Delete the question
          await api.delete(`/api/exam-questions`, {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
            params: {
              examId: examId,
              questionId: questionId
            }
          });

          // Refresh exam questions after deletion
          const refreshResponse = await api.get(`/api/exams/${examId}/with-questions`, {
            headers: {
              Accept: '*/*',
              Authorization: `Bearer ${idToken}`,
            },
          });

          const data = refreshResponse.data; // Axios automatically parses the response data
          setExamData(data);
        } catch (error) {
          console.error('Error deleting question:', error);
          setErrorMessage(
            'Failed to delete question'
          );
          setIsErrorPopupOpen(true);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
      }
      unsubscribe();
    });
  };

  const handleDeleteExam = async () => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error('Failed to get user ID token');
          }

          await api.delete(`/api/exams/${examId}`, {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
          });

          navigate('/exams-management'); // Redirect after successful deletion
        } catch (error) {
          console.error('Error deleting exam:', error);
          setErrorMessage(
            'Failed to delete exam'
          );
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
          setIsDeleteExamPopupOpen(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setIsDeleteExamPopupOpen(false);
      }
      unsubscribe();
    });
  };

  const handleBack = () => {
    navigate('/exams-management');
  };

  const confirmDeleteExam = () => {
    setIsDeleteExamPopupOpen(true);
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">{examData?.exam.examName || 'Exam'}</h1>
        <div className="flex space-x-4">
          <button
            onClick={confirmDeleteExam}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
          >
            <Trash2 className="h-5 w-5 mr-2" />
            Delete Exam
          </button>
          <button
            onClick={handleBack}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow"
          >
            Back
          </button>
        </div>
      </div>
      {examData ? (
        <>
          <div className="bg-white p-4 rounded-lg shadow-md mb-6">
            <h2 className="text-lg font-semibold text-gray-700 mb-2">Exam Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <p><strong>Date:</strong> {examData.exam.examDate ? new Date(examData.exam.examDate).toLocaleString() : 'N/A'}</p>
              <p><strong>Duration:</strong> {examData.exam.duration || 'N/A'} minutes</p>
              <p><strong>Total Marks:</strong> {examData.exam.totalMarks || 'N/A'}</p>
              <p><strong>Passing Marks:</strong> {examData.exam.passingMarks || '0'}</p>
              <p><strong>Total Questions:</strong> {examData.exam.totalQuestions || '0'}</p>
              <p><strong>Status:</strong> {examData.exam.status}</p>
              <p><strong>Type:</strong> {examData.exam.examType}</p>
            </div>
            {examData.exam.description && (
              <p className="mt-2"><strong>Description:</strong> {examData.exam.description}</p>
            )}
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">
            Questions <span className="text-gray-500">({examData.questions.length})</span>
          </h2>
          {examData.questions.length > 0 ? (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Question
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {examData.questions.map((question: any) => (
                    <tr key={question.questionId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {question.question.length > 50
                          ? `${question.question.substring(0, 50)}...`
                          : question.question}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">{question.mark || 'N/A'}</td>
                      <td className="px-6 py-4 text-right text-sm">
                        <button
                          onClick={() => handleDeleteQuestion(question.questionId)}
                          className="text-red-600 hover:text-red-800 flex items-center justify-end"
                          title="Delete Question"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-gray-500">No questions added yet.</p>
          )}
        </>
      ) : (
        <div className="text-center py-12">No data available.</div>
      )}

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => {
          setIsErrorPopupOpen(false);
        }}
      />

      <ErrorPopupModal
        isOpen={isDeleteExamPopupOpen}
        message="Are you sure you want to delete this exam? This action cannot be undone."
        onClose={() => setIsDeleteExamPopupOpen(false)}
        onOk={handleDeleteExam}
      />
    </div>
  );
};

export default ViewExamQuestions;
