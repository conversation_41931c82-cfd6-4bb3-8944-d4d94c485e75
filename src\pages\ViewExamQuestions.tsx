import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { auth } from '../firebase-config';
import { Trash2, Users, Play } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';
import { v4 as uuidv4 } from 'uuid';

const ViewExamQuestions: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const [examData, setExamData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isDeleteExamPopupOpen, setIsDeleteExamPopupOpen] = useState(false);
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false);
  const [bulkSubmissionResults, setBulkSubmissionResults] = useState<any>(null);
  const [showResultsModal, setShowResultsModal] = useState(false);
  const navigate = useNavigate();

  // Fetch exam questions
  useEffect(() => {
    if (!examId) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const response = await api.get(`/api/exams/${examId}/with-questions`, {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`,
            },
          });
          const data = response.data;;
          setExamData(data);
        } catch (err) {
          setErrorMessage(err instanceof Error ? err.message : 'Failed to fetch exam questions');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [examId]);

  const handleDeleteQuestion = async (questionId: string) => {
    if (!window.confirm('Are you sure you want to delete this question?')) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error('Failed to get user ID token');
          }

          // Delete the question
          await api.delete(`/api/exam-questions`, {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
            params: {
              examId: examId,
              questionId: questionId
            }
          });

          // Refresh exam questions after deletion
          const refreshResponse = await api.get(`/api/exams/${examId}/with-questions`, {
            headers: {
              Accept: '*/*',
              Authorization: `Bearer ${idToken}`,
            },
          });

          const data = refreshResponse.data; // Axios automatically parses the response data
          setExamData(data);
        } catch (error) {
          console.error('Error deleting question:', error);
          setErrorMessage(
            'Failed to delete question'
          );
          setIsErrorPopupOpen(true);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
      }
      unsubscribe();
    });
  };

  const handleDeleteExam = async () => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error('Failed to get user ID token');
          }

          await api.delete(`/api/exams/${examId}`, {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
          });

          navigate('/exams-management'); // Redirect after successful deletion
        } catch (error) {
          console.error('Error deleting exam:', error);
          setErrorMessage(
            'Failed to delete exam'
          );
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
          setIsDeleteExamPopupOpen(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setIsDeleteExamPopupOpen(false);
      }
      unsubscribe();
    });
  };

  const handleBack = () => {
    navigate('/exams-management');
  };

  const confirmDeleteExam = () => {
    setIsDeleteExamPopupOpen(true);
  };

  // Function to generate random answers with 70-91% accuracy
  const generateRandomAnswers = (questions: any[]) => {
    const accuracy = Math.random() * 0.21 + 0.7; // 70-91% accuracy
    const correctCount = Math.floor(questions.length * accuracy);

    return questions.map((question, index) => {
      const isCorrect = index < correctCount;
      let selectedAnswer;

      if (isCorrect) {
        selectedAnswer = question.correctAnswer;
      } else {
        // Select a random wrong answer
        const wrongOptions = question.options.filter((opt: string) => opt !== question.correctAnswer);
        selectedAnswer = wrongOptions[Math.floor(Math.random() * wrongOptions.length)] || question.options[0];
      }

      return {
        answerId: uuidv4(),
        questionId: question.questionId,
        selectedAnswer,
        isCorrect,
        marksObtained: isCorrect ? question.mark : 0,
        timeTaken: Math.floor(Math.random() * 60) + 30, // Random time between 30-90 seconds
        submittedAt: new Date().toISOString(),
        question,
      };
    });
  };

  // Function to submit exam for all students
  const handleBulkSubmitExam = async () => {
    if (!examData?.questions || examData.questions.length === 0) {
      setErrorMessage("No questions available for this exam");
      setIsErrorPopupOpen(true);
      return;
    }

    if (!window.confirm('Are you sure you want to submit this exam for ALL students? This will generate random results with 70-91% accuracy for each student.')) {
      return;
    }

    setIsBulkSubmitting(true);
    setBulkSubmissionResults(null);

    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error("User not logged in");
      }

      const idToken = await currentUser.getIdToken();
      const headers = {
        Authorization: `Bearer ${idToken}`,
        "Content-Type": "application/json",
      };

      // Fetch all students from the backend
      const studentsResponse = await api.get('/api/students', { headers });
      const students = studentsResponse.data;

      if (!students || students.length === 0) {
        throw new Error("No students found in the database");
      }

      const results = [];
      const duration = examData.exam.duration || 60; // Default 60 minutes

      // Submit exam for each student
      for (const student of students) {
        try {
          const attemptId = uuidv4();
          const startTime = new Date(Date.now() - duration * 60 * 1000); // Simulate start time
          const endTime = new Date();

          // Generate random answers for this student
          const studentAnswers = generateRandomAnswers(examData.questions);

          // Calculate results
          const correctAnswers = studentAnswers.filter(answer => answer.isCorrect).length;
          const totalAnswered = studentAnswers.length;
          const incorrect = totalAnswered - correctAnswers;
          const unattempted = 0; // All questions answered
          const accuracy = totalAnswered > 0 ? (correctAnswers / totalAnswered) * 100 : 0;
          const totalTimeSpent = Math.floor(Math.random() * duration * 60) + (duration * 60 * 0.5); // 50-100% of exam duration

          // Prepare exam attempt data
          const examAttemptData = {
            attemptId,
            studentId: student.uid || student.studentId,
            examId,
            score: correctAnswers,
            percentile: 0,
            accuracy,
            correct: correctAnswers,
            incorrect,
            unattempted,
            status: "COMPLETED",
            attemptDate: endTime.toISOString(),
            retakeCount: 0,
            startTime: startTime.toISOString(),
            endTime: endTime.toISOString(),
            totalTimeSpent,
          };

          // Prepare answers with attempt ID
          const answersWithQuestions = studentAnswers.map(answer => ({
            ...answer,
            attemptId,
            studentId: student.uid || student.studentId,
            examId,
          }));

          // Prepare mistakes for incorrect answers
          const mistakes = answersWithQuestions
            .filter(answer => !answer.isCorrect)
            .map(answer => ({
              mistakeId: uuidv4(),
              studentId: student.uid || student.studentId,
              examId,
              questionId: answer.questionId,
              incorrectAnswer: answer.selectedAnswer,
              correctAnswer: answer.question.correctAnswer,
              mistakeType: "INCORRECT_SELECTION",
              reviewed: false,
              timestamp: endTime.toISOString(),
              reviewComments: "",
            }));

          // Submit exam attempt
          await api.post('/api/student-exams', examAttemptData, { headers });

          // Submit individual answers
          await api.post('/api/student-answers/multiple', answersWithQuestions, { headers });

          // Submit mistakes if any
          if (mistakes.length > 0) {
            await api.post('/api/mistakes/multiple', mistakes, { headers });
          }

          results.push({
            studentId: student.uid || student.studentId,
            studentName: student.name,
            attemptId,
            score: correctAnswers,
            totalQuestions: examData.questions.length,
            accuracy: accuracy.toFixed(2),
            status: 'SUCCESS'
          });

        } catch (error) {
          console.error(`Error submitting exam for student ${student.name}:`, error);
          results.push({
            studentId: student.uid || student.studentId,
            studentName: student.name,
            attemptId: null,
            score: 0,
            totalQuestions: examData.questions.length,
            accuracy: 0,
            status: 'FAILED',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      setBulkSubmissionResults({
        totalStudents: students.length,
        successfulSubmissions: results.filter(r => r.status === 'SUCCESS').length,
        failedSubmissions: results.filter(r => r.status === 'FAILED').length,
        results
      });
      setShowResultsModal(true);

    } catch (error) {
      console.error("Error in bulk exam submission:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to submit exam for students");
      setIsErrorPopupOpen(true);
    } finally {
      setIsBulkSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">{examData?.exam.examName || 'Exam'}</h1>
        <div className="flex space-x-4">
          <button
            onClick={() => navigate(`/exam-results/${examId}`)}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
          >
            <Users className="h-5 w-5 mr-2" />
            View Results
          </button>
          <button
            onClick={handleBulkSubmitExam}
            disabled={isBulkSubmitting}
            className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
          >
            {isBulkSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              <>
                <Play className="h-5 w-5 mr-2" />
                Submit for All Students
              </>
            )}
          </button>
          <button
            onClick={confirmDeleteExam}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
          >
            <Trash2 className="h-5 w-5 mr-2" />
            Delete Exam
          </button>
          <button
            onClick={handleBack}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow"
          >
            Back
          </button>
        </div>
      </div>
      {examData ? (
        <>
          <div className="bg-white p-4 rounded-lg shadow-md mb-6">
            <h2 className="text-lg font-semibold text-gray-700 mb-2">Exam Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <p><strong>Date:</strong> {examData.exam.examDate ? new Date(examData.exam.examDate).toLocaleString() : 'N/A'}</p>
              <p><strong>Duration:</strong> {examData.exam.duration || 'N/A'} minutes</p>
              <p><strong>Total Marks:</strong> {examData.exam.totalMarks || 'N/A'}</p>
              <p><strong>Passing Marks:</strong> {examData.exam.passingMarks || '0'}</p>
              <p><strong>Total Questions:</strong> {examData.exam.totalQuestions || '0'}</p>
              <p><strong>Status:</strong> {examData.exam.status}</p>
              <p><strong>Type:</strong> {examData.exam.examType}</p>
            </div>
            {examData.exam.description && (
              <p className="mt-2"><strong>Description:</strong> {examData.exam.description}</p>
            )}
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">
            Questions <span className="text-gray-500">({examData.questions.length})</span>
          </h2>
          {examData.questions.length > 0 ? (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Question
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {examData.questions.map((question: any) => (
                    <tr key={question.questionId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {question.question.length > 50
                          ? `${question.question.substring(0, 50)}...`
                          : question.question}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">{question.mark || 'N/A'}</td>
                      <td className="px-6 py-4 text-right text-sm">
                        <button
                          onClick={() => handleDeleteQuestion(question.questionId)}
                          className="text-red-600 hover:text-red-800 flex items-center justify-end"
                          title="Delete Question"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-gray-500">No questions added yet.</p>
          )}
        </>
      ) : (
        <div className="text-center py-12">No data available.</div>
      )}

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => {
          setIsErrorPopupOpen(false);
        }}
      />

      <ErrorPopupModal
        isOpen={isDeleteExamPopupOpen}
        message="Are you sure you want to delete this exam? This action cannot be undone."
        onClose={() => setIsDeleteExamPopupOpen(false)}
        onOk={handleDeleteExam}
      />

      {/* Bulk Submission Results Modal */}
      {showResultsModal && bulkSubmissionResults && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="bg-blue-600 text-white px-6 py-4">
              <h2 className="text-xl font-bold flex items-center">
                <Users className="h-6 w-6 mr-2" />
                Bulk Exam Submission Results
              </h2>
            </div>

            <div className="p-6">
              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800">Total Students</h3>
                  <p className="text-2xl font-bold text-blue-600">{bulkSubmissionResults.totalStudents}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-800">Successful Submissions</h3>
                  <p className="text-2xl font-bold text-green-600">{bulkSubmissionResults.successfulSubmissions}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-red-800">Failed Submissions</h3>
                  <p className="text-2xl font-bold text-red-600">{bulkSubmissionResults.failedSubmissions}</p>
                </div>
              </div>

              {/* Detailed Results */}
              <div className="max-h-96 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Accuracy
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {bulkSubmissionResults.results.map((result: any, index: number) => (
                      <tr key={index} className={result.status === 'SUCCESS' ? 'hover:bg-green-50' : 'hover:bg-red-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {result.studentName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {result.score}/{result.totalQuestions}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {result.accuracy}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${result.status === 'SUCCESS'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {result.status}
                          </span>
                          {result.error && (
                            <p className="text-xs text-red-600 mt-1">{result.error}</p>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-gray-50 px-6 py-4 flex justify-end">
              <button
                onClick={() => setShowResultsModal(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewExamQuestions;
