import { useEffect, useState } from 'react';
import { useAuthStore } from './authStore';

export const useAuthStoreRehydrated = () => {
    const [isRehydrated, setIsRehydrated] = useState(false);

    useEffect(() => {
        const unsubscribe = useAuthStore.subscribe(() => {
            // Zustand persist rehydrates asynchronously; this runs when state changes
            setIsRehydrated(true);
        });
        // Trigger initial check
        if (useAuthStore.persist.hasHydrated()) {
            setIsRehydrated(true);
        }
        return () => unsubscribe();
    }, []);

    return isRehydrated ? useAuthStore.getState() : null;
};