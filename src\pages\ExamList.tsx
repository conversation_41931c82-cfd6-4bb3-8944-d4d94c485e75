import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Clock, Calendar, Award, Loader2, Search } from 'lucide-react';
import { auth } from '../firebase-config';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';

interface Exam {
  examId: string;
  examName: string;
  examDate: string | null;
  passingMarks: number;
  totalMarks: number | null;
  totalQuestions: number | null;
  duration: number | null;
  status: string;
  instructions: string;
  description: string;
  examType: string;
  examCategoryId: string;
  createdAt: string;
  updatedAt: string;
}

interface ExamCategory {
  examCategoryId: string;
  name: string;
  description: string;
}

function ExamList() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [categories, setCategories] = useState<ExamCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }

          // Fetch exams
          const examsResponse = await api.get("/api/exams", {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          });

          const examsData = await examsResponse.data;
          setExams(examsData);

          // Fetch exam categories
          const categoriesResponse = await api.get("/api/exam-categories", {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          });

          const categoriesData = await categoriesResponse.data;
          setCategories(categoriesData);

        } catch (err) {
          setErrorMessage(err instanceof Error ? err.message : 'Failed to fetch data');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change the tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  // Function to get category name by ID
  const getCategoryName = (categoryId: string): string => {
    const category = categories.find(cat => cat.examCategoryId === categoryId);
    return category ? category.name : categoryId; // Fallback to ID if name not found
  };

  const uniqueCategories = [...new Set(exams.map(exam => exam.examCategoryId))];
  const filteredExams = exams
    .filter(exam => selectedCategory === 'ALL' || exam.examCategoryId === selectedCategory)
    .filter(exam =>
      searchQuery === '' ||
      exam.examName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exam.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Available Exams</h1>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search exams..."
              className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            <option value="ALL">All Categories</option>
            {uniqueCategories.map((categoryId) => (
              <option key={categoryId} value={categoryId}>
                {getCategoryName(categoryId)}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {filteredExams.map((exam) => (
          <Link
            key={exam.examId}
            to={`/exams/${exam.examId}`}
            className="block bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200"
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {exam.examType}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {exam.status}
                </span>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">{exam.examName}</h3>
              <div className="mt-4 space-y-3">
                {exam.examDate && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="flex-shrink-0 mr-1.5 h-4 w-4" />
                    {new Date(exam.examDate).toLocaleDateString()}
                  </div>
                )}
                {exam.duration && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="flex-shrink-0 mr-1.5 h-4 w-4" />
                    {exam.duration} minutes
                  </div>
                )}
                {exam.totalMarks && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Award className="flex-shrink-0 mr-1.5 h-4 w-4" />
                    {exam.totalMarks} marks
                  </div>
                )}
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
              </div>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Category: {getCategoryName(exam.examCategoryId)}
                </p>
              </div>
              <div className="mt-6">
                <button className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  View Details
                </button>
              </div>
            </div>
          </Link>
        ))}
      </div>

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => {
          setIsErrorPopupOpen(false);
        }}
      />
    </div>
  );
}

export default ExamList;