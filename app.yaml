runtime: nodejs20
service: default

automatic_scaling:
  target_cpu_utilization: 0.65
  min_instances: 1
  max_instances: 2

env_variables:
  VITE_API_URL: "http://localhost:8080"  # Remove HTML-like tags

handlers:
  # Serve images with correct MIME types
  - url: /.*\.jpg$
    static_files: dist/\1
    upload: dist/.*\.jpg$
    mime_type: image/jpeg

  - url: /.*\.png$
    static_files: dist/\1
    upload: dist/.*\.png$
    mime_type: image/png

  - url: /.*\.svg$
    static_files: dist/\1
    upload: dist/.*\.svg$
    mime_type: image/svg+xml

  # Serve JS/CSS with correct MIME types
  - url: /.*\.js$
    static_files: dist/\1
    upload: dist/.*\.js$
    mime_type: application/javascript

  - url: /.*\.css$
    static_files: dist/\1
    upload: dist/.*\.css$
    mime_type: text/css

  # Fallback for static files
  - url: /(.*\..+)$
    static_files: dist/\1
    upload: dist/(.*\..+)$

  # Fallback to index.html for SPA routes
  - url: /.*
    static_files: dist/index.html
    upload: dist/index.html