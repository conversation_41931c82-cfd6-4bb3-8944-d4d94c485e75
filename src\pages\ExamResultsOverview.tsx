import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { auth, db } from '../firebase-config';
import { collection, addDoc } from 'firebase/firestore';
import { Users, Award, TrendingUp, ArrowLeft, Eye, Download, Upload, Save } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';
import { v4 as uuidv4 } from 'uuid';

interface StudentResult {
  attemptId: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  seatNo: string;
  dob: string;
  address: string;
  mobile: string;
  aadhar: string;
  gender: string;
  age: string;
  caste: string;
  handicap: string;
  score: number;
  accuracy: number;
  correct: number;
  incorrect: number;
  unattempted: number;
  status: string;
  attemptDate: string;
  totalTimeSpent: number;
}

interface ExamResultsData {
  exam: {
    examId: string;
    examName?: string;
    title?: string;
    totalQuestions: number;
    totalMarks: number;
    duration: number;
  };
  results: StudentResult[];
  statistics: {
    totalAttempts: number;
    averageScore: number;
    averageAccuracy: number;
    highestScore: number;
    lowestScore: number;
    passRate: number;
  };
}

const ExamResultsOverview: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [resultsData, setResultsData] = useState<ExamResultsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'score' | 'accuracy' | 'name' | 'date'>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [uploadedStudents, setUploadedStudents] = useState<any[]>([]);
  const [isUploadMode, setIsUploadMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Function to generate mock exam results for uploaded students
  const generateMockResults = (students: any[], examData: any): ExamResultsData => {
    const results: StudentResult[] = students.map((student) => {
      const attemptId = uuidv4();
      const accuracy = Math.random() * 0.21 + 0.7; // 70-91% accuracy
      const totalQuestions = 100; // Default to 50 questions
      const correctAnswers = Math.floor(totalQuestions * accuracy);
      const incorrectAnswers = totalQuestions - correctAnswers;
      const score = correctAnswers;

      // Generate random attempt date within last 30 days
      const randomDate = new Date();
      randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 30));

      // Generate random time spent (50-100% of exam duration)
      const examDuration = 100; // Default 100 minutes
      const timeSpent = Math.floor(Math.random() * (examDuration * 60 * 0.5)) + (examDuration * 60 * 0.5);

      return {
        attemptId,
        studentId: student.seatno || student.studentId || student.id || uuidv4(),
        studentName: student.name || student.studentName || 'Unknown Student',
        studentEmail: student.email || student.emailId || 'No email available',
        seatNo: student.seatno || student.seatNo || '',
        dob: student.dob || student.dateOfBirth || '',
        address: student.address || '',
        mobile: student.mobile || student.mobileNo || student.phone || '',
        aadhar: student.aadhar || student.aadharNo || '',
        gender: student.gender || '',
        age: student.age || '',
        caste: student.caste || '',
        handicap: student.handicap || student.disability || 'N',
        score,
        accuracy: accuracy * 100,
        correct: correctAnswers,
        incorrect: incorrectAnswers,
        unattempted: 0,
        status: 'COMPLETED',
        attemptDate: randomDate.toISOString(),
        totalTimeSpent: timeSpent,
      };
    });

    // Calculate statistics
    const scores = results.map(r => r.score);
    const accuracies = results.map(r => r.accuracy);
    const totalQuestions = examData?.questions?.length || 50;
    const passingScore = Math.floor(totalQuestions * 0.6); // 60% passing

    return {
      exam: {
        examId: examId || 'mock-exam',
        title: examData?.title || 'Mock Exam Results',
        totalQuestions,
        totalMarks: totalQuestions,
        duration: examData?.duration || 60,
      },
      results,
      statistics: {
        totalAttempts: results.length,
        averageScore: scores.reduce((a, b) => a + b, 0) / scores.length,
        averageAccuracy: accuracies.reduce((a, b) => a + b, 0) / accuracies.length,
        highestScore: Math.max(...scores),
        lowestScore: Math.min(...scores),
        passRate: (results.filter(r => r.score >= passingScore).length / results.length) * 100,
      },
    };
  };

  // Function to handle CSV file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim().toLowerCase());

        const students = lines.slice(1).map((line) => {
          const values = line.split(',').map(v => v.trim());
          const student: any = {};

          headers.forEach((header, i) => {
            if (values[i]) {
              // Map specific column headers from your CSV format
              const normalizedHeader = header.toLowerCase().replace(/[.\s]/g, '');

              switch (normalizedHeader) {
                case 'srno':
                case 'sr.no':
                  student.srNo = values[i];
                  break;
                case 'seatno':
                  student.seatno = values[i];
                  break;
                case 'name':
                  student.name = values[i];
                  break;
                case 'dob':
                  student.dob = values[i];
                  break;
                case 'address':
                  student.address = values[i];
                  break;
                case 'mobileno':
                case 'mobile':
                  student.mobile = values[i];
                  break;
                case 'email':
                  student.email = values[i];
                  break;
                case 'aadhar':
                  student.aadhar = values[i];
                  break;
                case 'gender':
                  student.gender = values[i];
                  break;
                case 'age':
                  student.age = values[i];
                  break;
                case 'caste':
                  student.caste = values[i];
                  break;
                case 'handicap':
                  student.handicap = values[i];
                  break;
                default:
                  student[header] = values[i];
              }
            }
          });

          return student;
        }).filter(student => student.name); // Only include students with names

        if (students.length === 0) {
          setErrorMessage('No valid student data found in CSV. Please ensure the CSV has at least a name column.');
          setIsErrorPopupOpen(true);
          return;
        }

        // Generate mock exam data
        const mockExamData = {
          title: 'Uploaded Students Mock Exam',
          questions: Array.from({ length: 50 }, (_, i) => ({ id: i + 1 })), // Mock 50 questions
          duration: 60,
        };

        const mockResults = generateMockResults(students, mockExamData);
        setResultsData(mockResults);
        setUploadedStudents(students);
        setIsUploadMode(true);
        setLoading(false);

      } catch (error) {
        console.error('Error parsing CSV:', error);
        setErrorMessage('Error parsing CSV file. Please ensure it\'s properly formatted.');
        setIsErrorPopupOpen(true);
      }
    };

    reader.readAsText(file);
  };

  // Function to trigger file upload
  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  // Function to save results to Firestore
  const handleSaveResults = async () => {
    console.log('Save button clicked');
    console.log('Results data:', resultsData);
    console.log('Current user:', auth.currentUser);

    if (!resultsData || !auth.currentUser) {
      const message = 'Unable to save results. Please ensure you are logged in and have results to save.';
      console.error(message);
      setErrorMessage(message);
      setIsErrorPopupOpen(true);
      return;
    }

    setIsSaving(true);
    try {
      const resultToSave = {
        examName: resultsData.exam.examName || resultsData.exam.title || 'Exam',
        examId: resultsData.exam.examId,
        totalQuestions: resultsData.exam.totalQuestions,
        totalMarks: resultsData.exam.totalMarks,
        results: resultsData.results,
        statistics: resultsData.statistics,
        createdBy: auth.currentUser.uid,
        createdAt: new Date().toISOString(),
        isUploadMode: isUploadMode,
        resultId: uuidv4()
      };

      console.log('Attempting to save to Firestore:', resultToSave);
      const docRef = await addDoc(collection(db, 'examResults'), resultToSave);
      console.log('Results saved successfully with ID:', docRef.id);

      // Show success message
      alert(`Results saved successfully! Document ID: ${docRef.id}`);
    } catch (error) {
      console.error('Error saving results:', error);
      const errorMsg = `Failed to save results: ${error instanceof Error ? error.message : String(error)}`;
      setErrorMessage(errorMsg);
      setIsErrorPopupOpen(true);
    } finally {
      setIsSaving(false);
    }
  };





  useEffect(() => {
    // Check if we have saved result data passed from SavedResults page
    const savedResultData = location.state?.savedResultData;
    if (savedResultData) {
      setResultsData({
        exam: {
          examId: savedResultData.examId,
          examName: savedResultData.examName,
          totalQuestions: savedResultData.totalQuestions,
          totalMarks: savedResultData.totalMarks,
          duration: 0
        },
        results: savedResultData.results,
        statistics: savedResultData.statistics
      });
      setIsUploadMode(savedResultData.isUploadMode || false);
      setLoading(false);
      return;
    }

    if (!examId) return;

    const fetchExamResults = async () => {
      try {
        setLoading(true);
        const currentUser = auth.currentUser;
        if (!currentUser) {
          throw new Error("User not logged in");
        }

        const idToken = await currentUser.getIdToken();
        const headers = {
          Authorization: `Bearer ${idToken}`,
          "Content-Type": "application/json",
        };

        // Fetch exam details
        const examResponse = await api.get(`/api/exams/${examId}`, { headers });
        const exam = examResponse.data;

        // Fetch all student attempts for this exam
        let attempts = [];
        try {
          const attemptsResponse = await api.get(`/api/student-exams/exam/${examId}`, { headers });
          attempts = attemptsResponse.data;
        } catch (error) {
          console.error('Error fetching attempts with /exam/ endpoint:', error);
          // Try alternative endpoint - fetch all attempts and filter
          try {
            const allAttemptsResponse = await api.get('/api/student-exams', { headers });
            const allAttempts = allAttemptsResponse.data;
            attempts = allAttempts.filter((attempt: any) => attempt.examId === examId);
          } catch (fallbackError) {
            console.error('Error fetching all attempts:', fallbackError);
            throw new Error('Unable to fetch exam attempts');
          }
        }

        // Fetch student details to get names
        let students = [];
        try {
          const studentsResponse = await api.get('/api/students', { headers });
          students = studentsResponse.data;
        } catch (error) {
          console.error('Error fetching students:', error);
          // If students endpoint fails, we'll still show results but with IDs instead of names
        }

        // Create a map of student IDs to student info (name and email)
        const studentMap = new Map();
        students.forEach((student: any) => {
          const studentInfo = {
            name: student.name,
            email: student.email
          };
          // Map both uid and studentId to handle different ID formats
          if (student.uid) {
            studentMap.set(student.uid, studentInfo);
          }
          if (student.studentId) {
            studentMap.set(student.studentId, studentInfo);
          }
        });

        // Debug logging to help identify the issue (remove in production)
        console.log('Students data:', students.slice(0, 2)); // Show first 2 students
        console.log('Student map size:', studentMap.size);
        console.log('Attempts count:', attempts.length);
        if (attempts.length > 0) {
          console.log('Sample attempt:', attempts[0]);
        }

        // Process results
        const results: StudentResult[] = attempts.map((attempt: any) => {
          let studentInfo = studentMap.get(attempt.studentId);

          // If no info found, try to find by different ID formats
          if (!studentInfo) {
            // Try to find student by any matching field
            const foundStudent = students.find((s: any) =>
              s.uid === attempt.studentId ||
              s.studentId === attempt.studentId ||
              s.id === attempt.studentId
            );
            if (foundStudent) {
              studentInfo = {
                name: foundStudent.name,
                email: foundStudent.email
              };
            }
          }

          if (!studentInfo) {
            console.log(`No student info found for studentId: ${attempt.studentId}`);
            if (students.length > 0) {
              console.log('Available student IDs:', students.slice(0, 3).map((s: any) => ({ uid: s.uid, studentId: s.studentId, name: s.name, email: s.email })));
            }
          }

          return {
            attemptId: attempt.attemptId,
            studentId: attempt.studentId,
            studentName: studentInfo?.name || `Student ${attempt.studentId}`,
            studentEmail: studentInfo?.email || 'No email available',
            score: attempt.score,
            accuracy: attempt.accuracy,
            correct: attempt.correct,
            incorrect: attempt.incorrect,
            unattempted: attempt.unattempted,
            status: attempt.status,
            attemptDate: attempt.attemptDate,
            totalTimeSpent: attempt.totalTimeSpent,
          };
        });

        // Calculate statistics
        const totalAttempts = results.length;
        const scores = results.map(r => r.score);
        const accuracies = results.map(r => r.accuracy);

        const statistics = {
          totalAttempts,
          averageScore: totalAttempts > 0 ? scores.reduce((a, b) => a + b, 0) / totalAttempts : 0,
          averageAccuracy: totalAttempts > 0 ? accuracies.reduce((a, b) => a + b, 0) / totalAttempts : 0,
          highestScore: totalAttempts > 0 ? Math.max(...scores) : 0,
          lowestScore: totalAttempts > 0 ? Math.min(...scores) : 0,
          passRate: totalAttempts > 0 ? (results.filter(r => r.score >= (exam.passingMarks || 0)).length / totalAttempts) * 100 : 0,
        };

        setResultsData({
          exam: {
            examId: exam.examId,
            examName: exam.examName,
            totalQuestions: exam.totalQuestions || 0,
            totalMarks: exam.totalMarks || 0,
            duration: exam.duration || 0,
          },
          results,
          statistics,
        });

      } catch (err) {
        console.error("Error fetching exam results:", err);
        setErrorMessage(err instanceof Error ? err.message : "Failed to fetch exam results");
        setIsErrorPopupOpen(true);
      } finally {
        setLoading(false);
      }
    };

    fetchExamResults();
  }, [examId, location.state]);

  // Load saved results when component mounts


  const handleSort = (field: 'score' | 'accuracy' | 'name' | 'date') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const sortedResults = resultsData?.results.sort((a, b) => {
    let aValue: any, bValue: any;

    switch (sortBy) {
      case 'score':
        aValue = a.score;
        bValue = b.score;
        break;
      case 'accuracy':
        aValue = a.accuracy;
        bValue = b.accuracy;
        break;
      case 'name':
        aValue = a.studentName.toLowerCase();
        bValue = b.studentName.toLowerCase();
        break;
      case 'date':
        aValue = new Date(a.attemptDate);
        bValue = new Date(b.attemptDate);
        break;
      default:
        return 0;
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  }) || [];

  const handleViewDetails = (attemptId: string) => {
    navigate(`/result`, {
      state: {
        attemptId,
        fromOverview: true
      }
    });
  };

  const handleExportResults = () => {
    if (!resultsData) return;

    const csvContent = [
      ['Seat No', 'Student Name', 'DOB', 'Address', 'Mobile No', 'Email ID', 'Aadhar', 'Gender', 'Age', 'Caste', 'Handicap', 'Score', 'Accuracy (%)', 'Correct', 'Incorrect', 'Unattempted', 'Time Spent (min)', 'Attempt Date'],
      ...sortedResults.map(result => [
        result.seatNo,
        result.studentName,
        result.dob,
        result.address,
        result.mobile,
        result.studentEmail,
        result.aadhar,
        result.gender,
        result.age,
        result.caste,
        result.handicap,
        result.score,
        result.accuracy.toFixed(2),
        result.correct,
        result.incorrect,
        result.unattempted,
        Math.round(result.totalTimeSpent / 60),
        new Date(result.attemptDate).toLocaleString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${resultsData.exam.examName || resultsData.exam.title || 'exam'}_results.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-gray-600">Loading exam results...</div>
      </div>
    );
  }

  if (!resultsData) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <button
                onClick={() => navigate('/exams-management')}
                className="flex items-center text-blue-600 hover:text-blue-800 mb-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Exam Management
              </button>
              <h1 className="text-2xl font-bold text-gray-800">Exam Results</h1>
            </div>
          </div>

          {/* Upload Section */}
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="mb-6">
              <Upload className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-800 mb-2">Upload Student List</h2>
              <p className="text-gray-600 mb-4">
                Upload a CSV file with student details to generate mock exam results instantly.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                CSV should contain columns: <strong>Sr.No, Seatno, Name, DOB, Address, Mobile No, Email, Aadhar, Gender, Age, Caste, Handicap</strong>
              </p>
            </div>

            <div className="space-y-4">
              <button
                onClick={triggerFileUpload}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow flex items-center mx-auto"
              >
                <Upload className="h-5 w-5 mr-2" />
                Choose CSV File
              </button>

              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="hidden"
              />

              <div className="text-xs text-gray-500">
                <p>Sample CSV format:</p>
                <div className="bg-gray-100 p-3 rounded mt-2 text-left inline-block max-w-full overflow-x-auto">
                  <code className="text-xs">
                    Sr.No,Seatno,Name,DOB,Address,Mobile No,Email,Aadhar,Gender,Age,Caste,Handicap<br />
                    1,440079,Bibinavare Nikita Shivaji,28-03-2002,At post takli,8605913909,<EMAIL>,************,Female,23,मांग,N<br />
                    2,440080,John Doe,15-05-2001,Mumbai,9876543210,<EMAIL>,123456789012,Male,24,General,N
                  </code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <button
            onClick={() => navigate('/exams-management')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Exam Management
          </button>
          <h1 className="text-2xl font-bold text-gray-800">
            {resultsData.exam.examName || resultsData.exam.title} - Results
            {isUploadMode && (
              <span className="ml-3 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                Mock Results from Upload
              </span>
            )}
          </h1>
        </div>
        <div className="flex space-x-3">
          {!isUploadMode && (
            <button
              onClick={triggerFileUpload}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
            >
              <Upload className="h-5 w-5 mr-2" />
              Upload Students
            </button>
          )}
          <button
            onClick={handleExportResults}
            className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow flex items-center"
          >
            <Download className="h-5 w-5 mr-2" />
            Export CSV
          </button>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>

      {/* Results Content */}
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Attempts</p>
              <p className="text-2xl font-bold text-gray-900">{resultsData.statistics.totalAttempts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-gray-900">{resultsData.statistics.averageScore.toFixed(1)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average Accuracy</p>
              <p className="text-2xl font-bold text-gray-900">{resultsData.statistics.averageAccuracy.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pass Rate</p>
              <p className="text-2xl font-bold text-gray-900">{resultsData.statistics.passRate.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Results Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Student Results</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Seat No
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('name')}
                >
                  Student Name {sortBy === 'name' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('score')}
                >
                  Score {sortBy === 'score' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('accuracy')}
                >
                  Accuracy {sortBy === 'accuracy' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Breakdown
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time Spent
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('date')}
                >
                  Attempt Date {sortBy === 'date' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedResults.map((result) => (
                <tr key={result.attemptId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {result.seatNo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {result.studentName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`font-semibold ${result.score >= (resultsData.exam.totalQuestions * 0.7) ? 'text-green-600' :
                      result.score >= (resultsData.exam.totalQuestions * 0.5) ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                      {result.score}/{resultsData.exam.totalQuestions}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`font-semibold ${result.accuracy >= 70 ? 'text-green-600' :
                      result.accuracy >= 50 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                      {result.accuracy.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <span className="text-green-600">✓{result.correct}</span>
                      <span className="text-red-600">✗{result.incorrect}</span>
                      <span className="text-gray-400">-{result.unattempted}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.round(result.totalTimeSpent / 60)} min
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(result.attemptDate).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                    <button
                      onClick={() => handleViewDetails(result.attemptId)}
                      className="text-blue-600 hover:text-blue-800 flex items-center justify-end"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {sortedResults.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No student results found for this exam.</p>
          </div>
        )}
      </div>

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage || 'An error occurred'}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => setIsErrorPopupOpen(false)}
      />
    </div>
  );
};

export default ExamResultsOverview;
