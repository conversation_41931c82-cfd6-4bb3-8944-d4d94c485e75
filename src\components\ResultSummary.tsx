// src/components/ResultSummary.tsx
import React, { FC } from "react";

interface ResultSummaryProps {
    correctAnswers: number;
    totalQuestions: number;
}

const ResultSummary: FC<ResultSummaryProps> = ({
    correctAnswers,
    totalQuestions,
}) => {
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);
    const performanceMessage =
        percentage >= 90
            ? "Excellent! Congratulations!"
            : percentage >= 80
                ? "Great job! Congratulations!"
                : percentage >= 70
                    ? "Good work! Congratulations!"
                    : percentage >= 50
                        ? "Congratulations!"
                        : "Better luck next time!";

    const messageColor =
        percentage >= 70 ? "text-green-600" : percentage >= 50 ? "text-yellow-600" : "text-red-600";

    return (
        <div className="bg-white rounded-lg shadow p-6 mt-6 mx-4">
            <p className={`text-lg font-bold ${messageColor} text-center`}>
                {performanceMessage}
            </p>
            <p className="text-center text-gray-700 mt-2">
                You scored <span className="font-bold">{percentage}%</span>.
            </p>
            <div className="mt-4 flex justify-center space-x-4">
                <div className="flex items-center space-x-2">
                    <span className="w-4 h-4 bg-gray-300 rounded-full" />
                    <span>Not Attempted</span>
                </div>
                <div className="flex items-center space-x-2">
                    <span className="w-4 h-4 bg-green-400 rounded-full" />
                    <span>Correct</span>
                </div>
                <div className="flex items-center space-x-2">
                    <span className="w-4 h-4 bg-red-400 rounded-full" />
                    <span>Wrong</span>
                </div>
            </div>
        </div>
    );
};

export default ResultSummary;
