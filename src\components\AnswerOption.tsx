// src/components/AnswerOption.tsx
import React, { FC } from "react";

interface AnswerOptionProps {
    option: string;
    isSelected: boolean;
    onSelect: () => void;
}

const AnswerOption: FC<AnswerOptionProps> = ({
    option,
    isSelected,
    onSelect,
}) => {
    return (
        <div
            className={`p-4 rounded-lg cursor-pointer shadow ${isSelected ? "bg-green-200 border-2 border-green-600" : "bg-gray-100"
                } hover:bg-gray-200`}
            onClick={onSelect}
        >
            <span>{option}</span>
        </div>
    );
};

export default AnswerOption;
