import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '../store/authStore';
import { Camera, CheckCircle } from 'lucide-react';
import { doc, updateDoc } from 'firebase/firestore';
import { db, auth } from '../firebase-config';
import { User } from '../types/user';

const profileSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().regex(/^[0-9]{10}$/, 'Invalid mobile number'),
  address: z.string().optional(),
  category: z.string().optional(),
  collegeName: z.string().optional(),
  gender: z.string().optional(),
  guardianContact: z.string().optional(),
  interestedCoachingProgram: z.string().optional(),
  lastClassAttended: z.string().optional(),
  professionalQualification: z.string().optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

function Profile() {
  const { user, setUser } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: user?.fullName || '',
      email: user?.email || '',
      phone: user?.phone?.toString() || '',
      address: user?.address || '',
      category: user?.category || '',
      collegeName: user?.collegeName || '',
      gender: user?.gender || '',
      guardianContact: user?.guardianContact || '',
      interestedCoachingProgram: user?.interestedCoachingProgram || '',
      lastClassAttended: user?.lastClassAttended || '',
      professionalQualification: user?.professionalQualification || '',
    },
  });

  const onSubmit = async (data: ProfileForm) => {
    try {
      if (auth.currentUser) {
        const userDocRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userDocRef, {
          ...data,
          phone: Number(data.phone),
        });
        setUser({ ...user, ...data, phone: Number(data.phone) } as User);
        alert('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      alert('Failed to update profile. Please try again later.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8 px-4">
      <div className="max-w-4xl mx-auto bg-gray-800 shadow-lg rounded-lg overflow-hidden">
        {/* Profile Header */}
        <div className="px-6 py-5 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-gray-100">Profile Information</h3>
          <p className="mt-1 text-sm text-gray-400">
            Update your personal information and preferences.
          </p>
        </div>

        {/* Profile Picture */}
        <div className="px-6 py-5 border-b border-gray-700">
          <div className="flex items-center space-x-5">
            <div className="relative">
              <img
                className="h-24 w-24 rounded-full object-cover"
                src={`https://ui-avatars.com/api/?name=${user?.fullName}` || user?.profilePicture}
                alt="Profile"
              />
              <button
                onClick={() => alert('Profile picture change not implemented yet')}
                className="absolute bottom-0 right-0 p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <Camera className="h-4 w-4" />
              </button>
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-100">{user?.fullName}</h2>
              <p className="text-sm text-gray-400">{user?.email}</p>
              {user?.verified && (
                <div className="mt-1 flex items-center text-sm text-green-400">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Verified Account
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="divide-y divide-gray-700">
          <div className="px-6 py-5 space-y-6">
            <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
              {[
                { id: 'fullName', label: 'Full Name' },
                { id: 'email', label: 'Email Address' },
                { id: 'phone', label: 'Mobile Number' },
                { id: 'address', label: 'Address' },
                { id: 'category', label: 'Category' },
                { id: 'collegeName', label: 'College Name' },
                { id: 'gender', label: 'Gender' },
                { id: 'guardianContact', label: 'Guardian Contact' },
                { id: 'interestedCoachingProgram', label: 'Interested Coaching Program' },
                { id: 'lastClassAttended', label: 'Last Class Attended' },
                { id: 'professionalQualification', label: 'Professional Qualification' },
              ].map((field) => (
                <div key={field.id}>
                  <label htmlFor={field.id} className="block text-sm font-medium text-gray-300">
                    {field.label}
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id={field.id}
                      className="w-full rounded-md border border-gray-600 bg-gray-700 text-gray-100 placeholder-gray-500 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      {...register(field.id as keyof ProfileForm)}
                    />
                    {errors[field.id as keyof ProfileForm] && (
                      <p className="mt-1 text-sm text-red-400">
                        {errors[field.id as keyof ProfileForm]?.message}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Form Actions */}
          <div className="px-6 py-4 bg-gray-800 text-right">
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Profile;
