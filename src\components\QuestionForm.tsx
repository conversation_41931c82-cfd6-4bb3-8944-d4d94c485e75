
import React, { useState, useEffect } from 'react';

interface QuestionFormProps {
    onSubmit: (formData: FormData) => void;
    initialData?: Question;
    isEdit?: boolean;
}

interface Question {
    questionNumber: number;
    question: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
    mark: number;
    negativeMark: number;
    duration: number;
    category: string;
    topic: string;
}

export const QuestionForm: React.FC<QuestionFormProps> = ({ onSubmit, initialData, isEdit = false }) => {
    const [formData, setFormData] = useState<Question>({
        questionNumber: 0,
        question: '',
        options: ['', '', '', ''],
        correctAnswer: '',
        explanation: '',
        mark: 1,
        negativeMark: 0,
        duration: 60,
        category: '',
        topic: '',
    });
    const [categories] = useState([
        { label: 'General Knowledge', value: 'general-knowledge' },
        { label: 'Quantitative Aptitude', value: 'quantitative-aptitude' },
        { label: 'Logical Reasoning and Analytical Ability', value: 'logical-reasoning' },
        { label: 'English Language / Verbal Ability', value: 'english-language' },
        { label: 'General Science', value: 'general-science' },
        { label: 'Computer Knowledge', value: 'computer-knowledge' },
        { label: 'Economics and Financial Awareness', value: 'economics-financial-awareness' },
        { label: 'History and Culture', value: 'history-culture' },
        { label: 'Geography', value: 'geography' },
        { label: 'Mental Ability and Data Interpretation', value: 'mental-ability-data-interpretation' },
        { label: 'Ethics and Social Responsibility', value: 'ethics-social-responsibility' },
        { label: 'Subject-Specific Knowledge', value: 'subject-specific-knowledge' },
    ]);

    const [topics, setTopics] = useState<string[]>([]);

    const categoryToTopicsMap: Record<string, string[]> = {
        'general-knowledge': [
            'National and International News', 'Politics', 'Sports', 'History',
            'Geography', 'Economy', 'Science and Technology', 'Environment',
            'Awards and Honors', 'Books and Authors'
        ],
        'quantitative-aptitude': [
            'Arithmetic', 'Algebra', 'Geometry', 'Number Series', 'Data Interpretation',
            'Time and Work', 'Speed and Distance', 'Profit and Loss', 'Simple and Compound Interest',
            'Ratio and Proportion'
        ],
        'logical-reasoning': [
            'Verbal Reasoning', 'Non-Verbal Reasoning', 'Puzzles', 'Series Completion',
            'Syllogisms', 'Coding-Decoding', 'Blood Relations', 'Direction Sense',
            'Seating Arrangement', 'Analogies'
        ],
        'english-language': [
            'Vocabulary', 'Reading Comprehension', 'Grammar', 'Sentence Correction',
            'Synonyms and Antonyms', 'Para-jumbles', 'Cloze Test', 'Fill in the Blanks',
            'Sentence Completion', 'Idioms and Phrases'
        ],
        'general-science': [
            'Physics', 'Chemistry', 'Biology', 'Environmental Science', 'Basic Scientific Principles',
            'Human Physiology', 'Ecology', 'Botany', 'Zoology'
        ],
        'computer-knowledge': [
            'Basic Computer Concepts', 'MS Office', 'Internet', 'Computer Hardware',
            'Software', 'Networking', 'Operating Systems', 'Database Management', 'Computer Applications'
        ],
        'economics-financial-awareness': [
            'Economic Policies', 'National Income', 'Budgeting', 'Monetary Policy',
            'Banking', 'Stock Market', 'Financial Systems', 'GST (Goods and Services Tax)',
            'Fiscal Policy', 'Indian and Global Economic Trends'
        ],
        'history-culture': [
            'Ancient Indian History', 'Medieval Indian History', 'Modern Indian History',
            'Freedom Struggle', 'Indian Culture and Heritage', 'Indian Art and Architecture',
            'Religious Movements', 'Famous Historical Figures', 'Indian Independence Movement'
        ],
        'geography': [
            'Physical Geography', 'Political Geography', 'World Geography', 'Indian Geography',
            'Climate', 'Natural Resources', 'Geographical Features', 'Geopolitical Relations',
            'Rivers, Mountains, and Deserts'
        ],
        'mental-ability-data-interpretation': [
            'Data Interpretation', 'Bar Graphs', 'Pie Charts', 'Tables', 'Line Graphs',
            'Venn Diagrams', 'Logical Deduction', 'Data Sufficiency', 'Mathematical Puzzles',
            'Percentage Calculations'
        ],
        'ethics-social-responsibility': [
            'Ethical Principles', 'Integrity', 'Social Responsibility', 'Values and Morals',
            'Public Service Ethics', 'Corruption and its Impact', 'Human Rights',
            'Corporate Social Responsibility (CSR)', 'Environment and Ethics', 'Moral Dilemmas'
        ],
        'subject-specific-knowledge': [
            'Engineering', 'Medical', 'Law'
        ],
    };

    const subjectSpecificTopicsMap: Record<string, string[]> = {
        'engineering': [
            'Mechanical Engineering', 'Electrical Engineering', 'Civil Engineering',
            'Computer Science Engineering', 'Electronics Engineering', 'Engineering Mathematics'
        ],
        'medical': [
            'Anatomy', 'Physiology', 'Pharmacology', 'Pathology', 'Microbiology', 'Surgery', 'Biochemistry'
        ],
        'law': [
            'Constitutional Law', 'Criminal Law', 'Civil Law', 'International Law',
            'Environmental Law', 'Labor Law'
        ],
    };

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleCategoryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedCategory = event.target.value;
        setFormData({
            ...formData,
            category: selectedCategory,
            topic: '' // Reset topic when category changes
        });

        setTopics(categoryToTopicsMap[selectedCategory] || []);
    };

    const handleTopicChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        setFormData({
            ...formData,
            topic: event.target.value
        });
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleOptionChange = (index: number, value: string) => {
        const newOptions = [...formData.options];
        newOptions[index] = value;
        setFormData(prev => ({ ...prev, options: newOptions }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData as unknown as FormData);
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <label htmlFor="question" className="block text-sm font-medium text-gray-700">Question</label>
                <textarea
                    id="question"
                    name="question"
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.question}
                    onChange={handleChange}
                    required
                />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700">Options</label>
                {formData.options.map((option, index) => (
                    <input
                        key={index}
                        type="text"
                        value={option}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder={`Option ${index + 1}`}
                        required
                    />
                ))}
            </div>

            <div>
                <label htmlFor="correctAnswer" className="block text-sm font-medium text-gray-700">Correct Answer</label>
                <select
                    id="correctAnswer"
                    name="correctAnswer"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.correctAnswer}
                    onChange={handleChange}
                    required
                >
                    <option value="">Select correct answer</option>
                    {formData.options.map((option, index) => (
                        <option key={index} value={option}>{option}</option>
                    ))}
                </select>
            </div>

            <div>
                <label htmlFor="explanation" className="block text-sm font-medium text-gray-700">Explanation</label>
                <textarea
                    id="explanation"
                    name="explanation"
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.explanation}
                    onChange={handleChange}
                    required
                />
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label htmlFor="mark" className="block text-sm font-medium text-gray-700">Mark</label>
                    <input
                        type="number"
                        id="mark"
                        name="mark"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        value={formData.mark}
                        onChange={handleChange}
                        required
                    />
                </div>

                <div>
                    <label htmlFor="negativeMark" className="block text-sm font-medium text-gray-700">Negative Mark</label>
                    <input
                        type="number"
                        id="negativeMark"
                        name="negativeMark"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        value={formData.negativeMark}
                        onChange={handleChange}
                        required
                    />
                </div>
            </div>

            <div>
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700">Duration (seconds)</label>
                <input
                    type="number"
                    id="duration"
                    name="duration"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.duration}
                    onChange={handleChange}
                    required
                />
            </div>

            <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category</label>
                <select
                    id="category"
                    name="category"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.category}
                    onChange={handleCategoryChange}
                    required
                >
                    <option value="">Select Category</option>
                    {categories.map((category) => (
                        <option key={category.value} value={category.value}>
                            {category.label}
                        </option>
                    ))}
                </select>
            </div>

            <div>
                <label htmlFor="topic" className="block text-sm font-medium text-gray-700">Topic</label>
                <select
                    id="topic"
                    name="topic"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    value={formData.topic}
                    onChange={handleTopicChange}
                    required
                    disabled={!formData.category}
                >
                    <option value="">Select Topic</option>
                    {topics.map((topic, index) => (
                        <option key={index} value={topic}>
                            {topic}
                        </option>
                    ))}
                </select>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
                <button
                    type="button"
                    onClick={() => onSubmit(formData as unknown as FormData)}
                    className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                    {isEdit ? 'Update' : 'Add'} Question
                </button>
            </div>
        </form>
    );
};
export default QuestionForm;