import React, { useState, useEffect } from 'react';
import { auth } from '../firebase-config';
import ExamCard from '../components/ExamCard';
import CreateTestFormExam from '../components/CreateTestFormExam';
import { Plus, Filter, Search, BookOpen } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal';
import api from '../axios';
import { ExamCategory } from '../types';

const TestManagementPage: React.FC = () => {
    const [exams, setExams] = useState<any[]>([]);
    const [categories, setCategories] = useState<ExamCategory[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    // Consolidated fetch for categories and exams
    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setIsLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) {
                        throw new Error("Failed to get user ID token");
                    }

                    // Fetch categories
                    const categoriesResponse = await api.get('/api/exam-categories', {
                        headers: {
                            'Accept': '*/*',
                            'Authorization': `Bearer ${idToken}`,
                        },
                    });
                    const categoriesData = await categoriesResponse.data;
                    setCategories(categoriesData);

                    // Fetch exams
                    const examsResponse = await api.get('/api/exams', {
                        headers: {
                            'Accept': '*/*',
                            'Authorization': `Bearer ${idToken}`,
                        },
                    });
                    const examsData = await examsResponse.data;
                    setExams(examsData);
                } catch (error) {
                    console.error('Error fetching data:', error);
                    setErrorMessage(error instanceof Error ? error.message : 'Failed to load data');
                    setIsErrorPopupOpen(true);
                } finally {
                    setIsLoading(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.");
                setIsErrorPopupOpen(true);
                setIsLoading(false);
            }
        });

        return () => unsubscribe();
    }, []); // Fetch both once on mount

    const handleCreateSuccess = () => {
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
            if (firebaseUser) {
                try {
                    setIsLoading(true);
                    const idToken = await firebaseUser.getIdToken();
                    if (!idToken) {
                        throw new Error("Failed to get user ID token");
                    }
                    const response = await api.get('/api/exams', {
                        headers: {
                            'Accept': '*/*',
                            'Authorization': `Bearer ${idToken}`,
                        },
                    });
                    const data = response.data;;
                    setExams(data);
                } catch (error) {
                    console.error('Error fetching exams after creation:', error);
                    setErrorMessage(error instanceof Error ? error.message : 'Failed to load exams');
                    setIsErrorPopupOpen(true);
                } finally {
                    setIsLoading(false);
                    // setCreateModalOpen(false);
                }
            } else {
                setErrorMessage("User not logged in, please change tab and try again.");
                setIsErrorPopupOpen(true);
                setIsLoading(false);
                setCreateModalOpen(false);
            }
            unsubscribe();
        });
    };

    const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedCategory(e.target.value);
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const uniqueCategoryIds = [...new Set(exams.map((exam) => exam.examCategoryId))];
    const categoryOptions = [
        { id: 'ALL', name: 'All Categories' },
        ...uniqueCategoryIds.map((id) => {
            const category = categories.find((cat) => cat.examCategoryId === id);
            return { id, name: category ? category.name : id };
        }),
    ];



    const filteredExams = exams
        .filter((exam) => selectedCategory === 'ALL' || exam.examCategoryId === selectedCategory)
        .filter((exam) =>
            searchQuery === '' ||
            (exam.examName || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
            (exam.examDescription || '').toLowerCase().includes(searchQuery.toLowerCase())
        );

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="py-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <BookOpen className="h-8 w-8 text-blue-600" />
                                <h1 className="ml-3 text-2xl font-bold text-gray-900">Exam Management</h1>
                            </div>
                            <button
                                onClick={() => setCreateModalOpen(true)}
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                            >
                                <Plus className="h-5 w-5 mr-2" />
                                Create New Exam
                            </button>
                        </div>

                        <div className="mt-6 flex flex-col sm:flex-row gap-4">
                            <div className="flex-1 max-w-xs">
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <input
                                        type="text"
                                        placeholder="Search exams..."
                                        value={searchQuery}
                                        onChange={handleSearchChange}
                                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    />
                                </div>
                            </div>
                            <div className="w-full sm:w-64">
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Filter className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <select
                                        value={selectedCategory}
                                        onChange={handleCategoryChange}
                                        className="block w-full pl-10 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                    >
                                        {categoryOptions.map((option) => (
                                            <option key={option.id} value={option.id}>
                                                {option.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {isLoading ? (
                    <div className="flex justify-center items-center min-h-[400px]">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                ) : filteredExams.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredExams.map((exam) => (
                            <ExamCard key={exam.examId} exam={exam} />
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No exams found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            {searchQuery || selectedCategory !== 'ALL'
                                ? 'Try adjusting your filters or search terms'
                                : 'Get started by creating a new exam'}
                        </p>
                        <div className="mt-6">
                            <button
                                onClick={() => setCreateModalOpen(true)}
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                <Plus className="h-5 w-5 mr-2" />
                                Create New Exam
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {isCreateModalOpen && (
                <CreateTestFormExam
                    isOpen={isCreateModalOpen}
                    onClose={() => setCreateModalOpen(false)}
                    onSuccess={handleCreateSuccess}
                />
            )}

            <ErrorPopupModal
                isOpen={isErrorPopupOpen}
                message={errorMessage || 'An error occurred'}
                onClose={() => setIsErrorPopupOpen(false)}
                onOk={() => {
                    setIsErrorPopupOpen(false);
                    window.location.reload(); // Changed to reload for retry consistency
                }}
            />
        </div>
    );
}

export default TestManagementPage;