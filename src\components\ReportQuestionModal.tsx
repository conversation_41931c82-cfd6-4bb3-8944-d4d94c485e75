import React, { useState } from "react";
import { AlertTriangle, X, Send, AlertCircle, CheckCircle } from 'lucide-react';

interface ReportQuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  questionId: string;
  onReport: (reportType: string, message: string) => Promise<void>;
}

const reportTypes = [
  { value: "WRONG_TRANSLATION", label: "Wrong Translation", icon: "🔤" },
  { value: "SCROLL_NOT_WORKING", label: "Scroll Not Working", icon: "📜" },
  { value: "WRONG_QUESTION", label: "Wrong Question", icon: "❓" },
  { value: "OUT_OF_SYLLABUS", label: "Out of Syllabus", icon: "📚" },
  { value: "QUESTION_OPTIONS_NOT_VISIBLE", label: "Question Options Not Visible", icon: "👁️" },
  { value: "BLINKING_SCREEN_ISSUE", label: "Blinking Screen Issue", icon: "💻" },
];

const ReportQuestionModal: React.FC<ReportQuestionModalProps> = ({
  isOpen,
  onClose,
  questionId,
  onReport,
}) => {
  const [selectedType, setSelectedType] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedType || !message.trim()) {
      setSubmissionStatus("Please select a report type and enter a message.");
      setIsSuccess(false);
      return;
    }

    setIsSubmitting(true);
    setSubmissionStatus(null);

    try {
      await onReport(selectedType, message);
      setSubmissionStatus("Report submitted successfully!");
      setIsSuccess(true);
      setTimeout(() => {
        onClose();
        setSelectedType("");
        setMessage("");
        setSubmissionStatus(null);
        setIsSuccess(false);
      }, 2000);
    } catch (error) {
      setSubmissionStatus("Failed to submit report. Please try again.");
      setIsSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-xl mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-white" />
            <h3 className="text-lg font-semibold text-white">Report Question</h3>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors focus:outline-none"
            disabled={isSubmitting}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            {/* Report Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                What issue are you reporting?
              </label>
              <div className="grid grid-cols-2 gap-3">
                {reportTypes.map((type) => (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => setSelectedType(type.value)}
                    className={`flex items-center p-3 rounded-lg border-2 transition-all ${
                      selectedType === type.value
                        ? 'border-red-500 bg-red-50 text-red-700'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    disabled={isSubmitting}
                  >
                    <span className="text-xl mr-2">{type.icon}</span>
                    <span className="text-sm font-medium">{type.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Message Input */}
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Describe the issue in detail
              </label>
              <div className="relative">
                <textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className={`block w-full rounded-lg border-2 shadow-sm transition-colors
                    ${message ? 'border-gray-300' : 'border-gray-200'}
                    focus:border-red-500 focus:ring-red-500 
                    placeholder:text-gray-400 resize-none`}
                  rows={4}
                  placeholder="Please provide specific details about the issue you're experiencing..."
                  disabled={isSubmitting}
                />
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {message.length}/500
                </div>
              </div>
            </div>

            {/* Status Message */}
            {submissionStatus && (
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                isSuccess ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
              }`}>
                {isSuccess ? (
                  <CheckCircle className="h-5 w-5 flex-shrink-0" />
                ) : (
                  <AlertCircle className="h-5 w-5 flex-shrink-0" />
                )}
                <p className="text-sm">{submissionStatus}</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-6 flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Submit Report
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReportQuestionModal;