// src/components/Legend.tsx
import React, { FC } from "react";

interface LegendProps {
    items: { color: string; label: string }[];
}

const Legend: FC<LegendProps> = ({ items }) => {
    return (
        <div className="p-4 border-t border-gray-200">
            <h3 className="text-sm font-semibold text-gray-700 mb-3">Legend</h3>
            <div className="flex flex-wrap items-center gap-4">
                {items.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span className={`w-4 h-4 rounded ${item.color}`} />
                        <span className="text-sm text-gray-600">{item.label}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Legend;
