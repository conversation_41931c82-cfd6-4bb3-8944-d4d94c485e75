import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { auth } from '../firebase-config';
import { User } from '../types';
import { NavigateFunction } from 'react-router-dom';
import api from '../axios';

interface AuthState {
  user: User | null;
  token: string | null;
  sessionId: string | null;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setSessionId: (sessionId: string | null) => void;
  logout: (navigate: NavigateFunction) => Promise<void>;
  refreshToken: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      sessionId: null,
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token }),
      setSessionId: (sessionId) => set({ sessionId }),
      logout: async (navigate) => {
        try {
          const { sessionId, token } = get();
          if (sessionId && token) {
            await api.post(
              '/logout-react',
              {},
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                  'X-Session-ID': sessionId,
                },
              }
            );
          }
          await auth.signOut();
          set({
            user: null,
            token: null,
            sessionId: null,
          });
          navigate('/login');
        } catch (error) {
          console.error('Error logging out:', error);
          await auth.signOut();
          set({
            user: null,
            token: null,
            sessionId: null,
          });
          navigate('/login');
        }
      },
      refreshToken: async () => {
        try {
          const firebaseUser = auth.currentUser;
          if (firebaseUser) {
            const newToken = await firebaseUser.getIdToken(true);
            set({ token: newToken });
          } else {
            throw new Error('No authenticated user found');
          }
        } catch (error) {
          console.error('Error refreshing token:', error);
          set({
            user: null,
            token: null,
            sessionId: null,
          });
        }
      },
    }),
    {
      name: 'auth-storage',
    }
  )
);