import React from 'react';
import { Loader2, X } from 'lucide-react';

const ExamDetailsModal = ({ isOpen, onClose, examData, loading }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-screen items-center justify-center p-4">
                {/* Overlay */}
                <div
                    className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    onClick={onClose}
                />

                {/* Modal Content */}
                <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full m-4">
                    <div className="p-6">
                        {/* Close Button */}
                        <button
                            onClick={onClose}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
                        >
                            <X className="h-6 w-6" />
                        </button>

                        {loading ? (
                            <div className="flex items-center justify-center py-12">
                                <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
                            </div>
                        ) : (
                            <>
                                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                                    {examData?.examName || 'Exam Details'}
                                </h2>

                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm text-gray-500">Exam ID</p>
                                            <p className="text-base font-medium">{examData?.examId}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Date & Time</p>
                                            <p className="text-base font-medium">
                                                {examData?.examDate ? new Date(examData.examDate).toLocaleString() : '-'}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Total Marks</p>
                                            <p className="text-base font-medium">{examData?.totalMarks || '-'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Passing Marks</p>
                                            <p className="text-base font-medium">{examData?.passingMarks || '-'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Total Questions</p>
                                            <p className="text-base font-medium">{examData?.totalQuestions || '-'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Status</p>
                                            <p className="text-base font-medium">{examData?.status || '-'}</p>
                                        </div>
                                    </div>

                                    {examData?.description && (
                                        <div>
                                            <p className="text-sm text-gray-500">Description</p>
                                            <p className="text-base">{examData.description}</p>
                                        </div>
                                    )}

                                    {examData?.instructions && (
                                        <div>
                                            <p className="text-sm text-gray-500">Instructions</p>
                                            <p className="text-base">{examData.instructions}</p>
                                        </div>
                                    )}
                                </div>

                                <div className="mt-6 flex justify-end">
                                    <button
                                        onClick={onClose}
                                        className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                                    >
                                        Close
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ExamDetailsModal;