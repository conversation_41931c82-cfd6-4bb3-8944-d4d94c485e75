import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { auth } from '../firebase-config';
import { Filter, ArrowLeft } from 'lucide-react';
import ErrorPopupModal from '../components/ErrorPopupModal'; // Import ErrorPopupModal
import axios from 'axios';
import api from '../axios';

const AddQuestionsToExam: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const [questions, setQuestions] = useState<any[]>([]);
  const [examQuestions, setExamQuestions] = useState<Set<string>>(new Set());
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string; topics: { id: string; name: string }[] }[]>([]);
  const [filters, setFilters] = useState({
    searchQuery: '',
    selectedCategory: 'all',
    selectedTopic: 'all',
    markRange: { min: '', max: '' },
  });
  const [loading, setLoading] = useState(true); // Start with true to keep loading until initial fetches complete
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const navigate = useNavigate();

  // Fetch categories
  useEffect(() => {
    if (!examId) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true); // Keep loading true until data is fetched
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const response = await api.get('/api/categories/with-topics', {
            headers: { 'Accept': '*/*', 'Authorization': `Bearer ${idToken}` },
          });
          const data = response.data;;
          setCategories(data);
        } catch (error) {
          console.error('Error fetching categories:', error);
          setErrorMessage(error instanceof Error ? error.message : 'Failed to load categories');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false); // Will be overridden by other fetches if still loading
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [examId]);

  // Fetch exam questions
  useEffect(() => {
    if (!examId) return;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true); // Keep loading true until data is fetched
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const response = await api.get(`/api/exams/${examId}/with-questions`, {
            headers: { 'Accept': '*/*', 'Authorization': `Bearer ${idToken}` },
          });
          const data = response.data;;
          setExamQuestions(new Set(data.questions.map((q: any) => q.questionId)));
        } catch (error) {
          console.error('Error fetching exam questions:', error);
          setErrorMessage(error instanceof Error ? error.message : 'Failed to load exam questions');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false); // Will be overridden by fetchQuestions if still loading
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [examId]);

  // Fetch available questions
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true); // Keep loading true until data is fetched
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          let url = '/api/questions?page=0&size=100';
          const { searchQuery, selectedCategory, selectedTopic, markRange } = filters;
          if (searchQuery) url += `&search=${encodeURIComponent(searchQuery)}`;
          if (selectedCategory !== 'all') url += `&categoryId=${selectedCategory}`;
          if (selectedTopic !== 'all') url += `&topicId=${selectedTopic}`;
          if (markRange.min) url += `&minMark=${markRange.min}`;
          if (markRange.max) url += `&maxMark=${markRange.max}`;
          const response = await api.get(url, {
            headers: { 'Accept': '*/*', 'Authorization': `Bearer ${idToken}` },
          });
          const data = response.data;;
          setQuestions(data.content);
        } catch (error) {
          console.error('Error fetching questions:', error);
          setErrorMessage(error instanceof Error ? error.message : 'Failed to load questions');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [filters]);

  const handleFilterChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    field: string,
    subField?: 'min' | 'max'
  ) => {
    const { value } = e.target;
    if (subField) {
      setFilters((prev) => ({
        ...prev,
        markRange: { ...prev.markRange, [subField]: value },
      }));
    } else {
      setFilters((prev) => ({
        ...prev,
        [field]: value,
        ...(field === 'selectedCategory' ? { selectedTopic: 'all' } : {}),
      }));
    }
  };

  const handleSelectQuestion = (questionId: string) => {
    setSelectedQuestions((prev) =>
      prev.includes(questionId) ? prev.filter((id) => id !== questionId) : [...prev, questionId]
    );
  };

  const handleAddQuestions = async () => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const payload = selectedQuestions.map((questionId) => ({
            examId,
            questionId,
          }));

          try {
            const response = await api.post(`/api/exams/${examId}/questions-list`, payload, {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${idToken}`,
              },
            });

            const responseData = response.data; // Axios automatically parses the response data
            navigate(`/exams_management/view-questions/${examId}`);
            // No need to manually check for response.ok, Axios throws errors for non-2xx responses
          } catch (error) {
            if (axios.isAxiosError(error)) {
              throw new Error(`Failed to add questions: ${error.response?.status}`);
            } else {
              throw new Error('An unexpected error occurred');
            }
          }

        } catch (error) {
          console.error('Error adding questions:', error);
          setErrorMessage(error instanceof Error ? error.message : 'Failed to add questions');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
      unsubscribe(); // Unsubscribe immediately after action
    });
  };

  const getTopicsForCategory = () => {
    const category = categories.find((cat) => cat.id === filters.selectedCategory);
    return category ? category.topics : [];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 relative pb-20">
      <div className="bg-white shadow-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                className="inline-flex items-center text-gray-700 hover:text-gray-900"
                onClick={() => navigate('/exams-management')}
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">Back</span>
              </button>
              <h1 className="ml-8 text-2xl font-bold text-gray-900">Add Questions to Exam</h1>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600 mr-4">
                Selected: {selectedQuestions.length} questions
              </span>
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-sm disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                onClick={handleAddQuestions}
                disabled={selectedQuestions.length === 0 || loading}
              >
                {loading ? 'Adding...' : 'Add Selected Questions'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <input
                type="text"
                placeholder="Search questions..."
                value={filters.searchQuery}
                onChange={(e) => handleFilterChange(e, 'searchQuery')}
                className="block w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <div className="relative">
                <Filter className="absolute left-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <select
                  value={filters.selectedCategory}
                  onChange={(e) => handleFilterChange(e, 'selectedCategory')}
                  className="block w-full pl-10 p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Topic</label>
              <div className="relative">
                <Filter className="absolute left-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <select
                  value={filters.selectedTopic}
                  onChange={(e) => handleFilterChange(e, 'selectedTopic')}
                  className="block w-full pl-10 p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  disabled={filters.selectedCategory === 'all'}
                >
                  <option value="all">All Topics</option>
                  {getTopicsForCategory().map((topic) => (
                    <option key={topic.id} value={topic.id}>
                      {topic.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Mark Range</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={filters.markRange.min}
                  onChange={(e) => handleFilterChange(e, 'markRange', 'min')}
                  className="block w-1/2 p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={filters.markRange.max}
                  onChange={(e) => handleFilterChange(e, 'markRange', 'max')}
                  className="block w-1/2 p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Question
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Topic
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Marks
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Difficulty
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {questions.map((question) => {
                  const category = categories.find((cat) => cat.id === question.categoryId);
                  const topic = category?.topics.find((t) => t.id === question.topicId);
                  return (
                    <tr key={question.questionId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedQuestions.includes(question.questionId)}
                          onChange={() => handleSelectQuestion(question.questionId)}
                          disabled={examQuestions.has(question.questionId)}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xl">
                          {question.question.length > 100
                            ? `${question.question.substring(0, 100)}...`
                            : question.question}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {category?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {topic?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {question.mark || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {question.difficultyLevel || 'N/A'}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        <ErrorPopupModal
          isOpen={isErrorPopupOpen}
          message={errorMessage || 'An error occurred'}
          onClose={() => setIsErrorPopupOpen(false)}
          onOk={() => {
            setIsErrorPopupOpen(false);
            window.location.reload(); // Retry on OK
          }}
        />
      </div>
    </div>
  );
};

export default AddQuestionsToExam;