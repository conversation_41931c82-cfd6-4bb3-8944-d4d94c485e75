import React, { useRef } from "react";

const ImportExportQuestions: React.FC = () => {
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            const fileContent = event.target?.result;
            alert("Questions imported successfully!");
        };
        reader.readAsText(file);
    };

    const handleExport = (format: "csv" | "json") => {
        const sampleData = [
            {
                questionNumber: 1,
                question: "What is the value of 25 + 36?",
                options: ["51", "61", "71", "81"],
                correctAnswer: "61",
                explanation: "25 + 36 = 61",
                mark: 1,
                negativeMark: -1,
                duration: 60,
                category: "general aptitude",
                topic: "addition",
            },
        ];

        let content = "";
        if (format === "csv") {
            content =
                "questionNumber,question,options,correctAnswer,explanation,mark,negativeMark,duration,category,topic\n";
            sampleData.forEach((q) => {
                content += `${q.questionNumber},"${q.question}","${q.options.join(";")}","${q.correctAnswer}","${q.explanation}",${q.mark},${q.negativeMark},${q.duration},"${q.category}","${q.topic}"\n`;
            });
        } else if (format === "json") {
            content = JSON.stringify(sampleData, null, 2);
        }

        const blob = new Blob([content], { type: format === "csv" ? "text/csv" : "application/json" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `questions.${format}`;
        link.click();
        URL.revokeObjectURL(url);
    };

    return (
        <div className="p-6 bg-white rounded shadow">
            <h2 className="text-xl font-bold mb-4">Import/Export Questions</h2>

            <div className="mb-4">
                <h3 className="font-medium mb-2">Import Questions:</h3>
                <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv,.json"
                    onChange={handleImport}
                    className="border p-2 rounded"
                />
                <p className="text-sm text-gray-500 mt-1">
                    Accepted formats: CSV, JSON
                </p>
            </div>

            <div className="mb-4">
                <h3 className="font-medium mb-2">Export Questions:</h3>
                <button
                    onClick={() => handleExport("csv")}
                    className="bg-green-500 text-white px-4 py-2 rounded mr-2"
                >
                    Export as CSV
                </button>
                <button
                    onClick={() => handleExport("json")}
                    className="bg-blue-500 text-white px-4 py-2 rounded"
                >
                    Export as JSON
                </button>
            </div>
        </div>
    );
};

export default ImportExportQuestions;
