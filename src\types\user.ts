// In types/user.ts
export interface User {
    id: string;
    email: string;
    name: string;
    role: string;
    verified: boolean;
    createdAt: string | undefined;
    fullName: string;
    address: string;
    category: string;
    collegeName: string;
    fcmToken: string;
    gender: string;
    guardianContact: string;
    interestedCoachingProgram: string | null;
    lastClassAttended: string | null;
    phone: number | null;
    professionalQualification: string | null;
    profilePicture: string;
    uid: string;
}
