import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

function VerifyCode() {
  const navigate = useNavigate();
  const [code, setCode] = useState(['', '', '', '']);
  const inputs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      // Move to next input if value is entered
      if (value && index < 3) {
        inputs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputs.current[index - 1]?.focus();
    }
  };

  const handleSubmit = async () => {
    const verificationCode = code.join('');
    if (verificationCode.length === 4) {
      try {
        // Here you would typically verify the code with your backend
        navigate('/login');
      } catch (error) {
        console.error('Verification failed:', error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-[#ECF4F4] p-5">
      <div className="safe-area">
        <button
          onClick={() => navigate(-1)}
          className="mb-8 inline-flex items-center justify-center w-11 h-11 rounded-full bg-[#228cdc] text-white hover:bg-[#1a7ac0] transition-colors"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>

        <div className="mt-5">
          <h1 className="text-xl font-medium">Verification code</h1>
          <p className="mt-1 text-base">
            Input the verification code we've sent to your e-mail
          </p>
        </div>

        <div className="mt-5">
          <div className="flex justify-between gap-4">
            {[0, 1, 2, 3].map((index) => (
              <div
                key={index}
                className="h-17 w-16 bg-white rounded-2xl overflow-hidden"
              >
                <input
                  ref={(el) => (inputs.current[index] = el)}
                  type="text"
                  value={code[index]}
                  onChange={(e) => handleChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className="w-full h-full text-center text-2xl font-semibold text-[#228cdc] border-none focus:ring-0 focus:outline-none bg-transparent"
                  maxLength={1}
                  inputMode="numeric"
                  pattern="\d*"
                />
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={handleSubmit}
          className="mt-10 w-full h-14 flex justify-center items-center px-4 rounded-3xl shadow-sm text-base font-medium text-white bg-[#228cdc] hover:bg-[#1a7ac0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#228cdc] transition-colors"
        >
          Submit
        </button>
      </div>
    </div>
  );
}

export default VerifyCode;