import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

function SessionValidation() {
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Here you would typically check the user's authentication status
        // and email verification status with your auth service
        const user = null; // Replace with actual auth check
        const isEmailVerified = false; // Replace with actual verification check

        if (user) {
          if (isEmailVerified) {
            navigate('/home');
          } else {
            navigate('/verify-email');
          }
        } else {
          navigate('/login');
        }
      } catch (error) {
        console.error('Session validation failed:', error);
        navigate('/login');
      }
    };

    checkAuthStatus();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#228cdc]"></div>
    </div>
  );
}

export default SessionValidation;