export type User = {
  id: string
  name: string
  email: string
  role: "admin" | "instructor" | "student"
  status: "active" | "inactive" | "suspended" | "access requested"
  lastLogin: string
  dateAdded: string
}

export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: '2024-03-10 14:30',
    dateAdded: '2024-01-15',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'instructor',
    status: 'active',
    lastLogin: '2024-03-09 09:15',
    dateAdded: '2024-02-01',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'student',
    status: 'suspended',
    lastLogin: '2024-03-08 16:45',
    dateAdded: '2024-02-15',
  },
  {
    id: '1',
    name: 'संदीप पाटील',
    email: '<EMAIL>',
    role: 'admin',
    status: 'access requested',
    lastLogin: '2025-01-10 10:30',
    dateAdded: '2024-12-20',
  },
  {
    id: '2',
    name: 'मीनल देशमुख',
    email: '<EMAIL>',
    role: 'instructor',
    status: 'active',
    lastLogin: '2025-01-09 14:15',
    dateAdded: '2024-11-01',
  },
  {
    id: '3',
    name: 'रोहन जगताप',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-15 18:45',
    dateAdded: '2024-12-25',
  },
  {
    id: '4',
    name: 'अमृता सावंत',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-12 08:00',
    dateAdded: '2024-12-30',
  },
  {
    id: '5',
    name: 'अजय काळे',
    email: '<EMAIL>',
    role: 'student',
    status: 'suspended',
    lastLogin: '2024-12-28 12:45',
    dateAdded: '2024-12-15',
  },
  {
    id: '6',
    name: 'सुमित गायकवाड',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-14 10:10',
    dateAdded: '2024-11-20',
  },
  {
    id: '7',
    name: 'श्रुती कदम',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-13 16:30',
    dateAdded: '2024-10-15',
  },
  {
    id: '8',
    name: 'अभिजीत माळी',
    email: '<EMAIL>',
    role: 'student',
    status: 'inactive',
    lastLogin: '2024-11-30 14:20',
    dateAdded: '2024-10-01',
  },
  {
    id: '9',
    name: 'प्रिया शिंदे',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-11 09:00',
    dateAdded: '2024-09-25',
  },
  {
    id: '10',
    name: 'सौरभ जाधव',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-09 11:15',
    dateAdded: '2024-08-20',
  },
  {
    id: '11',
    name: 'पूजा पवार',
    email: '<EMAIL>',
    role: 'student',
    status: 'suspended',
    lastLogin: '2024-12-26 13:45',
    dateAdded: '2024-07-15',
  },
  {
    id: '12',
    name: 'विक्रम नेवाळे',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-07 17:20',
    dateAdded: '2024-06-10',
  },
  {
    id: '13',
    name: 'स्नेहा मोहिते',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-10 15:50',
    dateAdded: '2024-05-05',
  },
  {
    id: '14',
    name: 'योगेश भोसले',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastLogin: '2025-01-08 20:30',
    dateAdded: '2024-04-25',
  },
  {
    id: '15',
    name: 'नेहा पाटील',
    email: '<EMAIL>',
    role: 'student',
    status: 'inactive',
    lastLogin: '2024-12-31 10:00',
    dateAdded: '2024-03-15',
  },
];
