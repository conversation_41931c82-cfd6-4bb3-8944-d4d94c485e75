// src/components/ProtectedRoute.tsx
import { useAuthStore } from '../store/authStore';
import { Navigate, Outlet } from 'react-router-dom';
import { ROLES } from '../constants/roles';

interface ProtectedRouteProps {
  allowedRoles: string[]; // Array of roles allowed to access the route
}

const ProtectedRoute = ({ allowedRoles }: ProtectedRouteProps) => {
  const { user } = useAuthStore();

  // If user is not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user's role is not allowed, redirect to unauthorized page
  if (!allowedRoles.includes(user.role)) {
    return <Navigate to="/unauthorized" replace />;
  }

  // If user is allowed, render the child routes
  return <Outlet />;
};

export default ProtectedRoute;