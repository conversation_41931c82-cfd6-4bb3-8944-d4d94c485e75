import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { GraduationCap } from 'lucide-react';
import { auth } from '../firebase-config';
import { signInWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from 'firebase/auth';
import api from '../axios'; // Custom Axios instance with interceptors
import ErrorPopupModal from '../components/ErrorPopupModal';

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type LoginForm = z.infer<typeof loginSchema>;

function Login() {
  const navigate = useNavigate();
  const { setUser, setToken, setSessionId, user } = useAuthStore();
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  useEffect(() => {
    if (user) navigate('/dashboard');
  }, [user, navigate]);

  const onSubmit = async (data: LoginForm) => {
    try {
      // Sign in with Firebase
      const { user: firebaseUser } = await signInWithEmailAndPassword(auth, data.email, data.password);
      const idToken = await firebaseUser.getIdToken();

      // Fetch user data from the backend
      const response = await api.get('/api/students/me', {
        headers: {
          Authorization: `Bearer ${idToken}`,
        },
      });

      // Extract user data from the response
      const userData = response.data;

      // Store session ID if available
      const sessionId = response.headers['x-session-id'];
      if (sessionId) {
        setSessionId(sessionId);
      }

      // Update user state with data from the API
      setUser({
        id: userData.studentId || firebaseUser.uid,
        email: userData.email || firebaseUser.email || '',
        name: userData.name || firebaseUser.displayName || 'Guest',
        role: userData.role || 'STUDENT',
        verified: firebaseUser.emailVerified,
        createdAt: userData.createdAt || firebaseUser.metadata.creationTime,
        fullName: userData.name || firebaseUser.displayName || '',
        address: userData.address || '',
        category: userData.category || '',
        collegeName: userData.collegeName || '',
        fcmToken: userData.fcmToken || '',
        gender: userData.gender || '',
        guardianContact: userData.guardianContact || '',
        interestedCoachingProgram: userData.interestedCoachingProgram || null,
        lastClassAttended: userData.lastClassAttended || null,
        phone: userData.phoneNumber || null,
        professionalQualification: userData.professionalQualification || null,
        profilePicture: firebaseUser.photoURL || '',
        uid: userData.uid || firebaseUser.uid,
      });

      // Navigate to the dashboard
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      let errorMessage = 'Login failed. Please try again.';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No user found with this email address.';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect password. Please try again.';
      } else if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Invalid credentials provided.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Unauthorized: Invalid token or session.';
      }
      setErrorMessage(errorMessage);
      setIsErrorPopupOpen(true);
    }
  };


  const handleGoogleSignIn = async () => {
    const provider = new GoogleAuthProvider();
    try {
      // Sign in with Google
      const result = await signInWithPopup(auth, provider);
      const firebaseUser = result.user;
      const idToken = await firebaseUser.getIdToken();

      // Fetch user data from the backend
      const response = await api.get('/api/students/me', {
        headers: {
          Authorization: `Bearer ${idToken}`,
        },
      });

      // Extract user data from the response
      const userData = response.data;

      // Store session ID if available
      const sessionId = response.headers['x-session-id'];
      if (sessionId) {
        setSessionId(sessionId);
      }

      // Update user state with data from the API
      setUser({
        id: userData.studentId || firebaseUser.uid,
        email: userData.email || firebaseUser.email || '',
        name: userData.name || firebaseUser.displayName || 'Guest',
        role: userData.role || 'STUDENT',
        verified: firebaseUser.emailVerified,
        createdAt: userData.createdAt || firebaseUser.metadata.creationTime,
        fullName: userData.name || firebaseUser.displayName || '',
        address: userData.address || '',
        category: userData.category || '',
        collegeName: userData.collegeName || '',
        fcmToken: userData.fcmToken || '',
        gender: userData.gender || '',
        guardianContact: userData.guardianContact || '',
        interestedCoachingProgram: userData.interestedCoachingProgram || null,
        lastClassAttended: userData.lastClassAttended || null,
        phone: userData.phoneNumber || null,
        professionalQualification: userData.professionalQualification || null,
        profilePicture: firebaseUser.photoURL || '',
        uid: userData.uid || firebaseUser.uid,
      });

      // Navigate to the dashboard
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Google sign-in error:', error);
      setErrorMessage('Google sign-in failed. Please try again.');
      setIsErrorPopupOpen(true);
    }
  };


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <GraduationCap className="h-12 w-12 text-primary-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link to="/register" className="font-medium text-primary-600 hover:text-primary-500">
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  {...register('email')}
                  aria-invalid={errors.email ? 'true' : 'false'}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email?.message?.toString()}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  type="password"
                  autoComplete="current-password"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  {...register('password')}
                  aria-invalid={errors.password ? 'true' : 'false'}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password?.message?.toString()}</p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="font-medium text-primary-600 hover:text-primary-500"
                >
                  Forgot your password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {isSubmitting ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>
            <div className="mt-6">
              <button
                onClick={handleGoogleSignIn}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <img
                  className="h-5 w-5"
                  src="https://www.google.com/favicon.ico"
                  alt="Google"
                />
                <span className="ml-2">Continue with Google</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => setIsErrorPopupOpen(false)}
      />
    </div>
  );
}

export default Login;