
import React, { FC, useState, useEffect } from "react";
import { Clock, X, Pause, Play } from "lucide-react";

interface AppBarProps {
    timeLeft: string; // Overall exam time remaining
    currentQuestion: number;
    totalQuestions: number;
    questionDuration: number;
    reportButton?: React.ReactNode; // Optional report button prop
}

const AppBar: FC<AppBarProps> = ({
    timeLeft,
    currentQuestion,
    totalQuestions,
    questionDuration,
    reportButton,
}) => {
    const [questionTimeLeft, setQuestionTimeLeft] = useState(questionDuration);
    const [isPaused, setIsPaused] = useState(false);
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

    // Reset timer when question or duration changes
    useEffect(() => {
        setQuestionTimeLeft(questionDuration);
    }, [currentQuestion, questionDuration]);

    // Countdown logic for question timer
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (!isPaused && questionTimeLeft > 0) {
            timer = setInterval(() => {
                setQuestionTimeLeft((prev) => prev - 1);
            }, 1000);
        }
        return () => clearInterval(timer);
    }, [isPaused, questionTimeLeft]);

    // Handle window resize
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= 768);
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Format time as MM:SS
    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    const togglePause = () => {
        setIsPaused((prev) => !prev);
    };

    const isTimeUp = questionTimeLeft <= 0;

    return (
        <div className="fixed top-0 left-0 right-0 z-20 bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg px-3 sm:px-6 py-2 sm:py-3">
            <div className="flex items-center justify-between">
                {/* Left Section */}
                <div className="flex items-center space-x-2">
                    <span className="text-xs sm:text-sm font-medium bg-blue-700/30 px-2 py-1 rounded-full">
                        Q {currentQuestion}/{totalQuestions}
                    </span>
                </div>

                {/* Center Section */}
                <div className="flex items-center space-x-3 sm:space-x-6">
                    {/* Exam Timer */}
                    <div className="flex items-center space-x-1 sm:space-x-2">
                        <Clock size={16} className="sm:w-5 sm:h-5 w-4 h-4" />
                        <span className="text-xs sm:text-sm font-semibold">
                            <span className="hidden sm:inline">Exam: </span>
                            {timeLeft}
                        </span>
                    </div>

                    {/* Question Timer */}
                    <div className="flex items-center space-x-1 sm:space-x-2">
                        <Clock
                            size={16}
                            className={`sm:w-5 sm:h-5 w-4 h-4 ${isTimeUp ? "text-red-500 animate-pulse" : "text-yellow-300"}`}
                        />
                        <span
                            className={`text-xs sm:text-sm font-semibold ${isTimeUp ? "text-red-500 animate-pulse" : ""}`}
                        >
                            <span className="hidden sm:inline">Current: </span>
                            {formatTime(questionTimeLeft)}
                        </span>
                    </div>
                </div>

                {/* Right Section */}
                <div className="flex items-center space-x-2">
                    {reportButton && <div className="mr-2">{reportButton}</div>}
                    <button
                        onClick={togglePause}
                        className="p-1 sm:p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none"
                        title={isPaused ? "Resume Timer" : "Pause Timer"}
                    >
                        {isPaused ? (
                            <Play size={18} className="sm:w-5 sm:h-5 w-4 h-4" />
                        ) : (
                            <Pause size={18} className="sm:w-5 sm:h-5 w-4 h-4" />
                        )}
                    </button>
                    <span className="text-xs sm:text-sm font-medium bg-green-700/30 px-2 py-1 rounded-full">
                        Live
                    </span>
                </div>
            </div>
        </div>
    );
};

export default AppBar;