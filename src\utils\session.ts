// File: ./utils/session.ts
import { auth, db } from '../firebase-config';
import { doc, updateDoc } from 'firebase/firestore';
import { useAuthStore } from '../store/authStore';

export const handleSessionLogout = async (userId: string) => {
    try {
        const userDocRef = doc(db, 'users', userId);
        await updateDoc(userDocRef, { activeSessionId: null });
        await signOut(auth);
        const authStore = useAuthStore.getState();
        authStore.setUser(null);
        authStore.setToken(null);
        authStore.setSessionId(null);
    } catch (error) {
        console.error('Failed to terminate session:', error);
    }
};