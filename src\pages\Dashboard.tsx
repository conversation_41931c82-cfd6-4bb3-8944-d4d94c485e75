import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../store/authStore';
import { BookOpen, Users, Award, Clock, Loader2 } from 'lucide-react';
import { format, eachDayOfInterval, isSameDay, isFirstDayOfMonth, startOfMonth, startOfYear, endOfYear } from 'date-fns';
import { auth } from '../firebase-config';
import ErrorPopupModal from '../components/ErrorPopupModal';
import ExamDetailsModal from '../components/ExamDetailsModal';
import { handleApiError } from '../utils/apiErrorHandler';
import { useNavigate } from 'react-router-dom';
import api from '../axios';

function Dashboard() {
  const { user } = useAuthStore();
  const currentYear = new Date().getFullYear();
  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [stats, setStats] = useState([
    { name: 'Total Exams', value: '-', icon: BookOpen },
    { name: 'Upcoming Exams', value: '-', icon: Clock },
    { name: 'Completed Exams', value: '-', icon: Award },
    { name: 'Active Students', value: '-', icon: Users },
  ]);
  const [examHistory, setExamHistory] = useState([]);
  const [upcomingExams, setUpcomingExams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isErrorPopupOpen, setIsErrorPopupOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isExamModalOpen, setIsExamModalOpen] = useState(false);
  const [examDetails, setExamDetails] = useState(null);
  const [examLoading, setExamLoading] = useState(false);

  const availableYears = Array.from({ length: 3 }, (_, i) => currentYear - i);

  const navigate = useNavigate();
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          setLoading(true);
          const idToken = await firebaseUser.getIdToken();
          if (!idToken) {
            throw new Error("Failed to get user ID token");
          }
          const statsResponse = await api.get('/api/dashboard/stats', {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          });

          if (handleApiError(statsResponse, navigate, setErrorMessage, setIsErrorPopupOpen)) {
            return;
          }
          const statsData = await statsResponse.data;
          setStats([
            { name: 'Total Exams', value: statsData.totalExams || '-', icon: BookOpen },
            { name: 'Upcoming Exams', value: statsData.upcomingExams || '-', icon: Clock },
            { name: 'Completed Exams', value: statsData.completedExams || '-', icon: Award },
            { name: 'Active Students', value: statsData.activeStudents || '-', icon: Users },
          ]);

          const activityResponse = await api.get(`/api/dashboard/exam-activity/${selectedYear}`, {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          });
          if (handleApiError(activityResponse, navigate, setErrorMessage, setIsErrorPopupOpen)) {
            return;
          }

          const activityData = await activityResponse.data;
          setExamHistory(activityData);

          const upcomingExamsResponse = await api.get('/api/dashboard/upcoming-exams', {
            headers: {
              'Accept': '*/*',
              'Authorization': `Bearer ${idToken}`
            }
          });
          if (handleApiError(upcomingExamsResponse, navigate, setErrorMessage, setIsErrorPopupOpen)) {
            return;
          }
          const upcomingExamsData = await upcomingExamsResponse.data;
          setUpcomingExams(upcomingExamsData);
        } catch (error) {
          console.error('Error fetching dashboard data:', error);
          setErrorMessage(error.message || 'Failed to load dashboard data. Please try again.');
          setIsErrorPopupOpen(true);
        } finally {
          setLoading(false);
        }
      } else {
        setErrorMessage("User not logged in, please change the tab and try again.");
        setIsErrorPopupOpen(true);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [selectedYear]);

  const fetchExamDetails = async (examId) => {
    try {
      setExamLoading(true);
      setIsExamModalOpen(true);
      const idToken = await auth.currentUser.getIdToken();

      const response = await api.get(`/api/exams/${examId}`, {
        headers: {
          'Accept': '*/*',
          'Authorization': `Bearer ${idToken}`
        }
      });

      if (handleApiError(response, navigate, setErrorMessage, setIsErrorPopupOpen)) {
        return;
      }

      const examData = response.data;;
      setExamDetails(examData);
    } catch (error) {
      console.error('Error fetching exam details:', error);
      setErrorMessage(error.message || 'Failed to load exam details.');
      setIsErrorPopupOpen(true);
      setIsExamModalOpen(false);
    } finally {
      setExamLoading(false);
    }
  };

  const dates = eachDayOfInterval({
    start: startOfYear(new Date(selectedYear, 0, 1)),
    end: selectedYear === currentYear ? new Date() : endOfYear(new Date(selectedYear, 0, 1)),
  });

  const getActivityLevel = (date) => {
    const activity = examHistory.find(h => isSameDay(new Date(h.date), date));
    return activity ? Math.min(4, activity.count) : 0;
  };

  const weeks = dates.reduce((acc, date) => {
    if (acc.length === 0 || acc[acc.length - 1].length === 7) acc.push([]);
    acc[acc.length - 1].push(date);
    return acc;
  }, []);
  const months = dates.reduce((acc, date) => {
    if (isFirstDayOfMonth(date)) acc.push(startOfMonth(date));
    return acc;
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div
            key={stat.name}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-medium text-gray-900">Exam Activity</h2>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
              className="block w-28 pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              {availableYears.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-600">Less</span>
            <div className="flex space-x-1">
              {[0, 1, 2, 3, 4].map((level) => (
                <div
                  key={level}
                  className={`h-4 w-4 rounded-sm ${level === 0
                    ? 'bg-gray-100'
                    : level === 1
                      ? 'bg-green-100'
                      : level === 2
                        ? 'bg-green-300'
                        : level === 3
                          ? 'bg-green-500'
                          : 'bg-green-700'
                    }`}
                />
              ))}
            </div>
            <span className="text-gray-600">More</span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <div className="relative">
            <div className="flex mb-2 text-xs text-gray-500">
              <div className="w-8" />
              <div className="flex-1">
                <div className="flex">
                  {months.map((date, index) => (
                    <div
                      key={index}
                      className="flex-shrink-0"
                      style={{
                        width: `${(weeks.length / months.length) * 20}px`,
                        marginLeft: index === 0 ? '0' : '-8px'
                      }}
                    >
                      {format(date, 'MMM')}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex">
              <div className="flex flex-col justify-between text-xs text-gray-500 pr-2">
                <div className="h-4">Mon</div>
                <div className="h-4">Wed</div>
                <div className="h-4">Fri</div>
              </div>

              <div className="inline-flex gap-1">
                {weeks.map((week, weekIndex) => (
                  <div key={weekIndex} className="flex flex-col gap-1">
                    {week.map((date, dateIndex) => {
                      const level = getActivityLevel(date);
                      return (
                        <div
                          key={dateIndex}
                          className={`h-4 w-4 rounded-sm cursor-pointer transition-colors
                                                    ${level === 0
                              ? 'bg-gray-100'
                              : level === 1
                                ? 'bg-green-100'
                                : level === 2
                                  ? 'bg-green-300'
                                  : level === 3
                                    ? 'bg-green-500'
                                    : 'bg-green-700'
                            }`}
                          title={`${format(date, 'EEEE, MMM d, yyyy')}: ${level === 0 ? 'No exams' : `${level} exam${level > 1 ? 's' : ''}`
                            }`}
                        />
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900">Upcoming Exams</h2>
        </div>
        <div className="border-t border-gray-200">
          <ul role="list" className="divide-y divide-gray-200">
            {upcomingExams.map((exam) => (
              <li key={exam.id} className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-primary-600 truncate">
                      {exam.title}
                    </p>
                    <p className="mt-1 text-sm text-gray-500">
                      {exam.date} at {exam.time} • {exam.duration}
                    </p>
                  </div>
                  <button
                    onClick={() => fetchExamDetails(exam.id)}
                    className="ml-4 px-3 py-1 text-sm text-primary-600 border border-primary-600 rounded-md hover:bg-primary-50"
                  >
                    View Details
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <ErrorPopupModal
        isOpen={isErrorPopupOpen}
        message={errorMessage}
        onClose={() => setIsErrorPopupOpen(false)}
        onOk={() => setIsErrorPopupOpen(false)}
      />
      <ExamDetailsModal
        isOpen={isExamModalOpen}
        onClose={() => {
          setIsExamModalOpen(false);
          setExamDetails(null);
        }}
        examData={examDetails}
        loading={examLoading}
      />
    </div>
  );
}

export default Dashboard;