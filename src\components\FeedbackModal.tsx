// src/components/FeedbackModal.tsx
import React, { FC } from "react";

interface FeedbackModalProps {
    question: string;
    yourAnswer: string;
    correctAnswer: string;
    explanation?: string;
    onClose: () => void;
}

const FeedbackModal: FC<FeedbackModalProps> = ({
    question,
    yourAnswer,
    correctAnswer,
    explanation,
    onClose,
}) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg shadow p-6 w-96">
                <h2 className="text-lg font-bold text-center">{question}</h2>
                <div className="mt-4">
                    <p className="font-bold text-green-500">Your Answer: {yourAnswer}</p>
                    <p className="font-bold text-blue-500">Correct Answer: {correctAnswer}</p>
                </div>
                {explanation && (
                    <div className="mt-4">
                        <p className="text-gray-600">{explanation}</p>
                    </div>
                )}
                <button
                    onClick={onClose}
                    className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
                >
                    Close
                </button>
            </div>
        </div>
    );
};

export default FeedbackModal;
