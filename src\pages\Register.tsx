import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, Link } from 'react-router-dom';
import { GraduationCap, Eye, EyeOff } from 'lucide-react';
import {
  createUserWithEmailAndPassword,
  sendEmailVerification,
  linkWithCredential,
  EmailAuthProvider,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
} from 'firebase/auth';
import { collection, addDoc, query, where, getDocs } from 'firebase/firestore';
import { auth, db } from '../firebase-config';

// Mock implementation of getFCMToken function
const getFCMToken = async (): Promise<string | null> => {
  return 'mock-fcm-token';
};

const registerSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name must be less than 50 characters'),
  email: z.string().email('Invalid email address').toLowerCase(),
  phoneNumber: z.string().regex(/^[0-9]{10}$/, 'Mobile number must be 10 digits'),
  guardianContact: z.string().regex(/^[0-9]{10}$/, 'Guardian contact must be 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  collegeName: z.string().min(2, 'College name must be at least 2 characters'),
  gender: z.enum(['Male', 'Female', 'Other']),
  lastClassAttended: z.enum(['BBA', 'BCA', 'SSC', 'HSC', 'BA', 'BCom', 'BSc', 'Diploma', 'Other']),
  professionalQualification: z.enum([
    'B.Ed', 'D.Ed', 'MBA', 'M.Ed', 'Ph.D', 'M.Phil', 'M.Tech', 'M.Sc', 'M.A', 'Other',
  ]),
  category: z.enum(['SC', 'ST', 'OBC', 'SBC', 'Open', 'Other']),
  coachingProgram: z.enum(['IBPS', 'LIC', 'MPSC', 'UPSC', 'Other']),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

type RegisterForm = z.infer<typeof registerSchema>;

function Register() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const location = useLocation();
  const { googleUser } = location.state || {};

  const { register, handleSubmit, setValue, formState: { errors } } = useForm<RegisterForm>({
    resolver: zodResolver(registerSchema),
  });

  // Pre-fill form fields if Google user data is available
  useEffect(() => {
    if (googleUser) {
      setValue('fullName', googleUser.fullName || '');
      setValue('email', googleUser.email || '');
    }
  }, [googleUser, setValue]);

  const genders = ['Male', 'Female', 'Other'];
  const lastClasses = ['BBA', 'BCA', 'SSC', 'HSC', 'BA', 'BCom', 'BSc', 'Diploma', 'Other'];
  const professionalQualifications = [
    'B.Ed', 'D.Ed', 'MBA', 'M.Ed', 'Ph.D', 'M.Phil', 'M.Tech', 'M.Sc', 'M.A', 'Other',
  ];
  const categories = ['SC', 'ST', 'OBC', 'SBC', 'Open', 'Other'];
  const coachingPrograms = ['IBPS', 'LIC', 'MPSC', 'UPSC', 'Other'];

  const prepareUserData = async (uid: string, data: RegisterForm) => {
    let fcmToken = null;
    try {
      fcmToken = await getFCMToken();
    } catch (error) {
      console.error('Failed to get FCM token:', error);
    }

    return {
      uid,
      FullName: data.fullName,
      email: data.email,
      phone: data.phoneNumber,
      role: 'USER' as const,
      fcmToken,
      gender: data.gender,
      guardianContact: data.guardianContact,
      address: data.address,
      lastClassAttended: data.lastClassAttended,
      professionalQualification: data.professionalQualification,
      collegeName: data.collegeName,
      category: data.category,
      interestedCoachingProgram: data.coachingProgram,
      createdAt: new Date().toISOString(),
      profilePicture: googleUser?.profilePicture || '',
    };
  };

  const onSubmit = async (data: RegisterForm) => {
    try {
      setIsLoading(true);
      setError(null);

      if (googleUser) {
        // Handle Google user registration
        if (!auth.currentUser) throw new Error('No authenticated user found');
        const userData = await prepareUserData(auth.currentUser.uid, data);
        await addDoc(collection(db, 'users'), userData);
        navigate('/dashboard');
      } else {
        // Handle email/password registration with linking support
        try {
          const userCredential = await createUserWithEmailAndPassword(
            auth,
            data.email,
            data.password
          );
          const userData = await prepareUserData(userCredential.user.uid, data);
          await addDoc(collection(db, 'users'), userData);
          await sendEmailVerification(userCredential.user, {
            url: `${window.location.origin}/login`,
            handleCodeInApp: true,
          });
          navigate('/verify-email', { state: { email: data.email } });
        } catch (error: any) {
          if (error.code === 'auth/email-already-in-use') {
            // Check if the email is tied to an existing Google account
            const userCollectionRef = collection(db, 'users');
            const userQuery = query(userCollectionRef, where('email', '==', data.email));
            const querySnapshot = await getDocs(userQuery);

            if (!querySnapshot.empty) {
              await signOut(auth); // Ensure no user is signed in
              const currentUser = (await signInWithPopup(auth, new GoogleAuthProvider())).user;
              const credential = EmailAuthProvider.credential(data.email, data.password);
              await linkWithCredential(currentUser, credential);

              const userData = await prepareUserData(currentUser.uid, data);
              await addDoc(collection(db, 'users'), userData); // Add user data if not already present
              navigate('/dashboard');
              return;
            }
          }
          throw error; // Re-throw if not handled by linking logic
        }
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      let errorMessage = 'Registration failed. Please try again.';
      if (error.code) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'This email is already registered. Try logging in or linking it.';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address format.';
            break;
          case 'auth/weak-password':
            errorMessage = 'Password must be at least 6 characters.';
            break;
          case 'auth/operation-not-allowed':
            errorMessage = 'Email/password authentication is not enabled.';
            break;
          case 'auth/requires-recent-login':
            errorMessage = 'Please log in again to link your account.';
            break;
        }
      } else if (error.message?.includes('Firestore')) {
        errorMessage = 'Error saving user data. Please contact support.';
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePassword = (field: 'password' | 'confirmPassword') => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <div className="flex justify-center">
            <GraduationCap className="h-12 w-12 text-primary-600" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Create Account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Please fill in your details to get started
          </p>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 bg-white p-8 rounded-lg shadow">
          {/* Personal Information Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <input
                  type="text"
                  id="fullName"
                  {...register('fullName')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  {...register('email')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  {...register('phoneNumber')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.phoneNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
                  Gender
                </label>
                <select
                  id="gender"
                  {...register('gender')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Select Gender</option>
                  {genders.map((gender) => (
                    <option key={gender} value={gender}>{gender}</option>
                  ))}
                </select>
                {errors.gender && (
                  <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="guardianContact" className="block text-sm font-medium text-gray-700">
                  Guardian Contact
                </label>
                <input
                  type="tel"
                  id="guardianContact"
                  {...register('guardianContact')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.guardianContact && (
                  <p className="mt-1 text-sm text-red-600">{errors.guardianContact.message}</p>
                )}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <textarea
                  id="address"
                  rows={3}
                  {...register('address')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.address && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Educational Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Educational Information</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="lastClassAttended" className="block text-sm font-medium text-gray-700">
                  Last Class Attended
                </label>
                <select
                  id="lastClassAttended"
                  {...register('lastClassAttended')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Select Last Class</option>
                  {lastClasses.map((cls) => (
                    <option key={cls} value={cls}>{cls}</option>
                  ))}
                </select>
                {errors.lastClassAttended && (
                  <p className="mt-1 text-sm text-red-600">{errors.lastClassAttended.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="professionalQualification" className="block text-sm font-medium text-gray-700">
                  Post-Graduation Qualification
                </label>
                <select
                  id="professionalQualification"
                  {...register('professionalQualification')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Select Qualification</option>
                  {professionalQualifications.map((qual) => (
                    <option key={qual} value={qual}>{qual}</option>
                  ))}
                </select>
                {errors.professionalQualification && (
                  <p className="mt-1 text-sm text-red-600">{errors.professionalQualification.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="collegeName" className="block text-sm font-medium text-gray-700">
                  College Name
                </label>
                <input
                  type="text"
                  id="collegeName"
                  {...register('collegeName')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
                {errors.collegeName && (
                  <p className="mt-1 text-sm text-red-600">{errors.collegeName.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Program Details */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Program Details</h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="category"
                  {...register('category')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Select Category</option>
                  {categories.map((cat) => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="coachingProgram" className="block text-sm font-medium text-gray-700">
                  Coaching Program
                </label>
                <select
                  id="coachingProgram"
                  {...register('coachingProgram')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Select Program</option>
                  {coachingPrograms.map((program) => (
                    <option key={program} value={program}>{program}</option>
                  ))}
                </select>
                {errors.coachingProgram && (
                  <p className="mt-1 text-sm text-red-600">{errors.coachingProgram.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Password Section - Hidden for Google users initially */}
          {!googleUser && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Security</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <div className="mt-1 relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      {...register('password')}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 pr-10"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => togglePassword('password')}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                    Confirm Password
                  </label>
                  <div className="mt-1 relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      id="confirmPassword"
                      {...register('confirmPassword')}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 pr-10"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => togglePassword('confirmPassword')}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="pt-6">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? 'Creating account...' : 'Create Account'}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <Link to="/login" className="font-medium text-primary-600 hover:text-primary-500">
                Log In
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Register;