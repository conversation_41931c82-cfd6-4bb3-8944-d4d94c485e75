// src/components/FinishButton.tsx
import React, { FC } from "react";

interface FinishButtonProps {
    onClick: () => void;
}

const FinishButton: FC<FinishButtonProps> = ({ onClick }) => {
    return (
        <button
            onClick={onClick}
            className="w-full py-2 bg-green-500 text-white text-lg font-semibold rounded hover:bg-green-600 transition"
        >
            Finish Exam
        </button>
    );
};

export default FinishButton;
