// src/components/QuestionNavigatorPanel.tsx
import React, { FC } from "react";
import QuestionGrid from "./QuestionGrid";
import Legend from "./Legend";
import FinishButton from "./FinishButton";

interface QuestionNavigatorPanelProps {
    isOpen: boolean;
    questions: { id: number; isAnswered: boolean; isCurrent: boolean }[];
    currentQuestionIndex: number;
    onClose: () => void;
    onQuestionSelect: (index: number) => void;
    onFinish: () => void;
}

const QuestionNavigatorPanel: FC<QuestionNavigatorPanelProps> = ({
    isOpen,
    questions,
    currentQuestionIndex,
    onClose,
    onQuestionSelect,
    onFinish,
}) => {
    return (
        <div
            className={`fixed top-0 right-0 h-full bg-white shadow-lg transition-transform duration-300 ${isOpen ? "translate-x-0" : "translate-x-full"
                } w-[25%] min-w-[300px]`}
        >
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                <h2 className="text-lg font-bold text-gray-700">Question Navigator</h2>
                <button
                    onClick={onClose}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none"
                >
                    ✕
                </button>
            </div>

            {/* Question Grid */}
            <QuestionGrid
                questions={questions}
                onQuestionSelect={onQuestionSelect}
            />

            {/* Legend */}
            <Legend
                items={[
                    { color: "bg-gray-300", label: "Not Attempted" },
                    { color: "bg-green-300", label: "Answered" },
                    { color: "bg-blue-500", label: "Current" },
                ]}
            />

            {/* Finish Button */}
            <div className="p-4">
                <FinishButton onClick={onFinish} />
            </div>
        </div>
    );
};

export default QuestionNavigatorPanel;
