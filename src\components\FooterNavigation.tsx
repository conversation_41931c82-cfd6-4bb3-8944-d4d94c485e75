// src/components/FooterNavigation.tsx
import React, { FC } from "react";

interface FooterNavigationProps {
    onPrevious: () => void;
    onNext: () => void;
    onFinish: () => void;
    isLastQuestion: boolean;
    isFirstQuestion: boolean;
    isNextEnabled: boolean;
}

const FooterNavigation: FC<FooterNavigationProps> = ({
    onPrevious,
    onNext,
    onFinish,
    isLastQuestion,
    isFirstQuestion,
    isNextEnabled,
}) => {
    return (
        <div className="flex justify-between px-4 py-2">
            <button
                onClick={onPrevious}
                disabled={isFirstQuestion}
                className={`px-4 py-2 rounded-md ${isFirstQuestion ? "bg-gray-200" : "bg-gray-800 text-white"
                    }`}
            >
                Previous
            </button>
            {isLastQuestion ? (
                <button
                    onClick={onFinish}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md"
                >
                    Finish
                </button>
            ) : (
                <button
                    onClick={onNext}
                    disabled={!isNextEnabled}
                    className={`px-4 py-2 rounded-md ${isNextEnabled ? "bg-blue-600 text-white" : "bg-gray-200"
                        }`}
                >
                    Next
                </button>
            )}
        </div>
    );
};

export default FooterNavigation;
