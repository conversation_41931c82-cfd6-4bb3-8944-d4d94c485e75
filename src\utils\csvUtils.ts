import type { User } from "../data/mockUsers"

export async function importUsersFromCSV(file: File): Promise<User[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (event) => {
      const csv = event.target?.result as string
      const lines = csv.split("\n")
      const headers = lines[0].split(",")
      const users: User[] = []

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(",")
        if (values.length === headers.length) {
          const user: User = {
            id: String(Date.now() + i), // Generate a unique ID
            name: values[1],
            email: values[2],
            role: values[3] as "admin" | "instructor" | "student",
            status: values[4] as "active" | "inactive" | "suspended",
            lastLogin: values[5],
            dateAdded: values[6],
          }
          users.push(user)
        }
      }

      resolve(users)
    }

    reader.onerror = (error) => reject(error)
    reader.readAsText(file)
  })
}

export function exportUsersToCSV(users: User[]) {
  const headers = ["Name", "Email", "Role", "Status", "Last Login", "Date Added"]
  const csvContent = [
    headers.join(","),
    ...users.map((user) => [user.name, user.email, user.role, user.status, user.lastLogin, user.dateAdded].join(",")),
  ].join("\n")

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  const link = document.createElement("a")
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", "users.csv")
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

