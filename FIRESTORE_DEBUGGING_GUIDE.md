# Firestore Debugging Guide

## Issue: Save button not showing saved results

### Steps to Debug:

1. **Check Browser Console**:
   - Open Developer Tools (F12)
   - Go to Console tab
   - Click the "Save Results" button
   - Look for any error messages

2. **Check Firestore Security Rules**:
   - Go to Firebase Console: https://console.firebase.google.com/
   - Select your project: `examate-d71a7`
   - Go to Firestore Database
   - Click on "Rules" tab
   - Ensure rules allow authenticated users to read/write

   **Recommended Rules:**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Allow authenticated users to read/write their own exam results
       match /examResults/{document} {
         allow read, write: if request.auth != null && request.auth.uid == resource.data.createdBy;
         allow create: if request.auth != null && request.auth.uid == request.resource.data.createdBy;
       }
       
       // Allow other collections as needed
       match /{document=**} {
         allow read, write: if request.auth != null;
       }
     }
   }
   ```

3. **Test Save Functionality**:
   - Go to Exam Results page
   - Click "Save Results" button
   - Check console for logs:
     - "Save button clicked"
     - "Attempting to save to Firestore"
     - "Results saved successfully with ID: ..."

4. **Test Load Functionality**:
   - Go to "Saved Results" page in sidebar
   - Click "Debug" button
   - Check console for:
     - Current user info
     - Total documents count
     - Documents for current user

5. **Manual Firestore Check**:
   - Go to Firebase Console
   - Go to Firestore Database
   - Look for "examResults" collection
   - Check if documents exist with your user ID

### Common Issues:

1. **Authentication**: User not logged in
2. **Security Rules**: Firestore rules blocking writes
3. **Network**: Connection issues
4. **Data Structure**: Missing required fields

### Debug Commands:

In browser console, you can run:
```javascript
// Check current user
console.log('Current user:', firebase.auth().currentUser);

// Check Firestore connection
firebase.firestore().collection('examResults').get().then(snapshot => {
  console.log('Collection size:', snapshot.size);
});
```
