# Firestore Debugging Guide

## Issue: Save button not showing saved results

### Steps to Debug:

1. **Check Browser Console**:
   - Open Developer Tools (F12)
   - Go to Console tab
   - Click the "Save Results" button
   - Look for any error messages

2. **Check Firestore Security Rules**:
   - Go to Firebase Console: https://console.firebase.google.com/
   - Select your project: `examate-d71a7`
   - Go to Firestore Database
   - Click on "Rules" tab
   - Ensure rules allow authenticated users to read/write

   **Updated Rules (add this to your existing rules):**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {

       match /users/{docId} {
         function isAdmin() {
           return request.auth.uid in [
             "BQ42cx6rCRRQBA7nsWPLU4Yt2fh1",  // admin1
             "kpzwNuxdJnM1Lp1LsPMyxtFXEi72",
             "PtBLQPV766dN5o7qNdlIzqLdPM93",// admin3
             "2tsF3YsA40YAkpb67ICpHq7jJBA2",// admin4
             "DRXqwwhKKyb633Ezlmeb6u12IOe2",// admin5
           ];
         }

         allow create: if request.auth != null;
         allow read, update, delete: if request.auth != null &&
                                     (resource.data.uid == request.auth.uid || isAdmin());
       }

       // ADD THIS SECTION FOR EXAM RESULTS
       match /examResults/{document} {
         function isAdmin() {
           return request.auth.uid in [
             "BQ42cx6rCRRQBA7nsWPLU4Yt2fh1",  // admin1
             "kpzwNuxdJnM1Lp1LsPMyxtFXEi72",
             "PtBLQPV766dN5o7qNdlIzqLdPM93",// admin3
             "2tsF3YsA40YAkpb67ICpHq7jJBA2",// admin4
             "DRXqwwhKKyb633Ezlmeb6u12IOe2",// admin5
           ];
         }

         // Allow create if user is authenticated and sets themselves as createdBy
         allow create: if request.auth != null &&
                       request.auth.uid == request.resource.data.createdBy;

         // Allow read/update/delete if user owns the document or is admin
         allow read, update, delete: if request.auth != null &&
                                     (resource.data.createdBy == request.auth.uid || isAdmin());
       }

     }
   }
   ```

3. **Test Save Functionality**:
   - Go to Exam Results page
   - Click "Save Results" button
   - Check console for logs:
     - "Save button clicked"
     - "Attempting to save to Firestore"
     - "Results saved successfully with ID: ..."

4. **Test Load Functionality**:
   - Go to "Saved Results" page in sidebar
   - Click "Debug" button
   - Check console for:
     - Current user info
     - Total documents count
     - Documents for current user

5. **Manual Firestore Check**:
   - Go to Firebase Console
   - Go to Firestore Database
   - Look for "examResults" collection
   - Check if documents exist with your user ID

### Common Issues:

1. **Authentication**: User not logged in
2. **Security Rules**: Firestore rules blocking writes
3. **Network**: Connection issues
4. **Data Structure**: Missing required fields

### Debug Commands:

In browser console, you can run:
```javascript
// Check current user
console.log('Current user:', firebase.auth().currentUser);

// Check Firestore connection
firebase.firestore().collection('examResults').get().then(snapshot => {
  console.log('Collection size:', snapshot.size);
});
```
