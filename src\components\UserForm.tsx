import React from 'react';
import { User } from '../types'; // Ensure the correct path to your User type

type UserFormProps = {
  onSubmit: (data: FormData) => void;
  initialData?: Partial<User>;
  isEdit?: boolean;
};

export function UserForm({ onSubmit, initialData, isEdit }: UserFormProps) {
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit(new FormData(e.currentTarget));
      }}
      className="max-w-4xl mx-auto p-6 bg-white shadow-lg rounded-lg space-y-6"
    >
      {/* Scrollable container for form fields */}
      <div className="overflow-y-auto max-h-[500px] pr-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Full Name */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Full Name</label>
            <input
              type="text"
              name="fullName"
              defaultValue={initialData?.fullName || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
              required
            />
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Email</label>
            <input
              type="email"
              name="email"
              defaultValue={initialData?.email || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
              required
            />
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Phone</label>
            <input
              type="text"
              name="phone"
              defaultValue={initialData?.phone || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Address</label>
            <input
              type="text"
              name="address"
              defaultValue={initialData?.address || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Category</label>
            <input
              type="text"
              name="category"
              defaultValue={initialData?.category || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* College Name */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">College Name</label>
            <input
              type="text"
              name="collegeName"
              defaultValue={initialData?.collegeName || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Gender */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Gender</label>
            <select
              name="gender"
              defaultValue={initialData?.gender || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 bg-white focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            >
              <option value="">Select Gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Guardian Contact */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Guardian Contact</label>
            <input
              type="text"
              name="guardianContact"
              defaultValue={initialData?.guardianContact || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Interested Coaching Program */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Interested Coaching Program</label>
            <input
              type="text"
              name="interestedCoachingProgram"
              defaultValue={initialData?.interestedCoachingProgram || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Last Class Attended */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Last Class Attended</label>
            <input
              type="text"
              name="lastClassAttended"
              defaultValue={initialData?.lastClassAttended || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Professional Qualification */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Professional Qualification</label>
            <input
              type="text"
              name="professionalQualification"
              defaultValue={initialData?.professionalQualification || ""}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            />
          </div>

          {/* Role */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Role</label>
            <select
              name="role"
              defaultValue={initialData?.role || "student"}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 bg-white focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            >
              <option value="admin">Admin</option>
              <option value="instructor">Instructor</option>
              <option value="student">Student</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-semibold text-gray-700">Status</label>
            <select
              name="status"
              defaultValue={initialData?.status || "active"}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 bg-white focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
              <option value="access requested">Access Requested</option>
            </select>
          </div>

          {/* Password (only for new users) */}
          {!isEdit && (
            <div className="md:col-span-2">
              <label className="block text-sm font-semibold text-gray-700">Password</label>
              <input
                type="password"
                name="password"
                className="mt-1 block w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
                required
              />
            </div>
          )}

          {/* Read-only timestamp fields (for editing) */}
          {isEdit && initialData && (
            <>
              <div>
                <label className="block text-sm font-semibold text-gray-700">Created At</label>
                <input
                  type="text"
                  name="createdAt"
                  defaultValue={initialData.createdAt || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-100 p-2"
                  disabled
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700">Last Login</label>
                <input
                  type="text"
                  name="lastLogin"
                  defaultValue={(initialData.lastLogin as string) || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-100 p-2"
                  disabled
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700">Date Added</label>
                <input
                  type="text"
                  name="dateAdded"
                  defaultValue={(initialData.dateAdded as string) || ""}
                  className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-100 p-2"
                  disabled
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end pt-4">
        <button
          type="submit"
          className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors duration-200"
        >
          {isEdit ? 'Update User' : 'Create User'}
        </button>
      </div>
    </form>
  );
}
