import React from 'react';
import { Link } from 'react-router-dom';
import {
    GraduationCap,
    BookOpen,
    Clock,
    Award,
    Users,
    CheckCircle,
    <PERSON><PERSON>hart,
    Brain,
    ArrowRight
} from 'lucide-react';

function Landing() {
    const features = [
        {
            icon: BookOpen,
            title: 'Comprehensive Exam Library',
            description: 'Access a vast collection of practice tests and mock exams for various competitive examinations.',
        },
        {
            icon: Clock,
            title: 'Real-time Assessment',
            description: 'Experience exam conditions with timed tests and instant performance evaluation.',
        },
        {
            icon: Brain,
            title: 'Smart Learning Analytics',
            description: 'Track your progress with detailed analytics and personalized improvement suggestions.',
        },
        {
            icon: Bar<PERSON>hart,
            title: 'Performance Tracking',
            description: 'Monitor your progress over time with detailed statistics and improvement metrics.',
        },
    ];

    const testimonials = [
        {
            content: "The practice tests helped me understand my weak areas and improve significantly. I cleared my IBPS exam in the first attempt!",
            author: "<PERSON><PERSON>",
            role: "IBPS PO",
            image: "https://images.unsplash.com/photo-*************-be9c29b29330?w=120&h=120&fit=crop"
        },
        {
            content: "The real-time assessment and detailed analytics helped me prepare better. Highly recommended for serious aspirants!",
            author: "<PERSON><PERSON><PERSON>",
            role: "Railway Group D",
            image: "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=120&h=120&fit=crop"
        },
        {
            content: "The mistake bank feature is brilliant! It helped me identify and work on my weak areas effectively.",
            author: "Anjali Patel",
            role: "CTET Qualified",
            image: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=120&h=120&fit=crop"
        },
    ];

    return (
        <div className="bg-white">
            {/* Hero Section */}
            <div className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-600 to-primary-800 opacity-90" />
                <div
                    className="absolute inset-0 bg-cover bg-center"
                    style={{
                        backgroundImage: 'url(https://images.unsplash.com/photo-*************-8df90110c9f1?auto=format&fit=crop&w=1920&q=80)',
                        filter: 'brightness(0.4)'
                    }}
                />
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
                    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
                        <div className="sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left">
                            <div className="flex items-center justify-center lg:justify-start mb-8">
                                <GraduationCap className="h-12 w-12 text-white" />
                                <span className="ml-3 text-2xl font-bold text-white">ExaMate</span>
                            </div>
                            <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                                Master Your
                                <span className="block text-primary-200">Exam Preparation</span>
                            </h1>
                            <p className="mt-3 text-base text-gray-100 sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">
                                Comprehensive practice tests, real-time assessment, and personalized analytics to help you succeed in competitive exams.
                            </p>
                            <div className="mt-8 sm:flex sm:justify-center lg:justify-start">
                                <div className="rounded-md shadow">
                                    <Link
                                        to="/login"
                                        className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
                                    >
                                        Get Started
                                    </Link>
                                </div>
                                <div className="mt-3 sm:mt-0 sm:ml-3">
                                    <Link
                                        to="/login"
                                        className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 md:py-4 md:text-lg md:px-10"
                                    >
                                        Sign In
                                    </Link>
                                </div>
                            </div>
                        </div>
                        <div className="mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center">
                            <div className="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                                <img
                                    className="w-full rounded-lg"
                                    src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?auto=format&fit=crop&w=800&q=80"
                                    alt="Student studying"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Stats Section */}
            <div className="bg-primary-800">
                <div className="max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8 lg:py-20">
                    <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="text-center">
                            <div className="flex items-center justify-center">
                                <Users className="h-8 w-8 text-primary-200" />
                            </div>
                            <p className="mt-2 text-3xl font-extrabold text-white">50,000+</p>
                            <p className="mt-1 text-base text-primary-200">Active Students</p>
                        </div>
                        <div className="text-center">
                            <div className="flex items-center justify-center">
                                <BookOpen className="h-8 w-8 text-primary-200" />
                            </div>
                            <p className="mt-2 text-3xl font-extrabold text-white">1,000+</p>
                            <p className="mt-1 text-base text-primary-200">Practice Tests</p>
                        </div>
                        <div className="text-center">
                            <div className="flex items-center justify-center">
                                <Award className="h-8 w-8 text-primary-200" />
                            </div>
                            <p className="mt-2 text-3xl font-extrabold text-white">95%</p>
                            <p className="mt-1 text-base text-primary-200">Success Rate</p>
                        </div>
                        <div className="text-center">
                            <div className="flex items-center justify-center">
                                <CheckCircle className="h-8 w-8 text-primary-200" />
                            </div>
                            <p className="mt-2 text-3xl font-extrabold text-white">10+</p>
                            <p className="mt-1 text-base text-primary-200">Exam Categories</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="py-16 bg-gray-50 overflow-hidden lg:py-24">
                <div className="relative max-w-xl mx-auto px-4 sm:px-6 lg:px-8 lg:max-w-7xl">
                    <div className="relative">
                        <h2 className="text-center text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                            A better way to prepare for exams
                        </h2>
                        <p className="mt-4 max-w-3xl mx-auto text-center text-xl text-gray-500">
                            Everything you need to excel in your competitive exams, all in one platform.
                        </p>
                    </div>

                    <div className="relative mt-12 lg:mt-20 lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
                        <div className="mt-10 space-y-10">
                            {features.map((feature) => (
                                <div key={feature.title} className="relative">
                                    <div className="flex items-center space-x-4">
                                        <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                                            <feature.icon className="h-6 w-6" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-medium text-gray-900">{feature.title}</h3>
                                            <p className="mt-1 text-sm text-gray-500">{feature.description}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="mt-10 -mx-4 relative lg:mt-0">
                            <img
                                className="relative mx-auto rounded-lg shadow-lg"
                                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80"
                                alt="Team collaborating"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Testimonials Section */}
            <div className="bg-white py-16 lg:py-24">
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="relative">
                        <h2 className="text-center text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                            What our students say
                        </h2>
                        <p className="mt-4 max-w-3xl mx-auto text-center text-xl text-gray-500">
                            Success stories from students who achieved their goals with ExaMate.
                        </p>
                    </div>

                    <div className="mt-12 grid gap-8 lg:grid-cols-3">
                        {testimonials.map((testimonial, index) => (
                            <div
                                key={index}
                                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                            >
                                <div className="px-6 py-8">
                                    <div className="relative">
                                        <svg
                                            className="absolute top-0 left-0 transform -translate-x-3 -translate-y-2 h-8 w-8 text-gray-200"
                                            fill="currentColor"
                                            viewBox="0 0 32 32"
                                            aria-hidden="true"
                                        >
                                            <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                                        </svg>
                                        <p className="relative text-lg text-gray-600">{testimonial.content}</p>
                                    </div>
                                    <div className="mt-6 flex items-center">
                                        <img
                                            className="h-12 w-12 rounded-full"
                                            src={testimonial.image}
                                            alt={testimonial.author}
                                        />
                                        <div className="ml-4">
                                            <div className="text-base font-medium text-gray-900">{testimonial.author}</div>
                                            <div className="text-sm text-primary-600">{testimonial.role}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* CTA Section */}
            <div className="bg-primary-700">
                <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
                    <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
                        <span className="block">Ready to get started?</span>
                        <span className="block text-primary-200">Join thousands of successful students today.</span>
                    </h2>
                    <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
                        <div className="inline-flex rounded-md shadow">
                            <Link
                                to="/register"
                                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-white hover:bg-primary-50"
                            >
                                Get started
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Landing;